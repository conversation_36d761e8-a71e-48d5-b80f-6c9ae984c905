package services

import (
	"encoding/json"
	"solve-go-api/internal/models"

	"gorm.io/gorm"
)

// MatchService 题目匹配服务
type MatchService struct {
	db *gorm.DB
}

// NewMatchService 创建匹配服务
func NewMatchService(db *gorm.DB) *MatchService {
	return &MatchService{db: db}
}

// MatchQuestion 根据题目信息匹配数据库中的题目
func (s *MatchService) MatchQuestion(saveQuestion *models.SaveQuestion) ([]models.QuestionBank, error) {
	// 1. 根据题目类型进行筛选
	typeCode := s.getQuestionTypeCode(saveQuestion.Type)
	if typeCode == 0 {
		return nil, nil
	}

	var candidates []models.QuestionBank
	err := s.db.Where("verified = ? AND type = ?", true, typeCode).Find(&candidates).Error
	if err != nil {
		return nil, err
	}

	if len(candidates) == 0 {
		return nil, nil
	}

	// 2. 根据字符长度进行筛选
	lengthFiltered := s.filterByLength(candidates, saveQuestion.QuestionLen)
	if len(lengthFiltered) == 0 {
		return nil, nil
	}

	// 3. 根据选项交集进行筛选
	optionFiltered := s.filterByOptions(lengthFiltered, saveQuestion.Options)

	return optionFiltered, nil
}

// getQuestionTypeCode 获取题目类型代码
func (s *MatchService) getQuestionTypeCode(questionType string) int {
	switch questionType {
	case "判断题":
		return 1
	case "单选题":
		return 2
	case "多选题":
		return 3
	default:
		return 0
	}
}

// filterByLength 根据字符长度筛选
func (s *MatchService) filterByLength(candidates []models.QuestionBank, targetLength int) []models.QuestionBank {
	var filtered []models.QuestionBank

	for _, candidate := range candidates {
		// 长度相差不超过±5字符
		if abs(candidate.QuestionLen-targetLength) <= 5 {
			filtered = append(filtered, candidate)
		}
	}

	return filtered
}

// filterByOptions 根据选项交集筛选
func (s *MatchService) filterByOptions(candidates []models.QuestionBank, userOptions map[string]string) []models.QuestionBank {
	var filtered []models.QuestionBank

	for _, candidate := range candidates {
		// 解析数据库中的选项
		var dbOptions map[string]string
		if err := json.Unmarshal(candidate.Options, &dbOptions); err != nil {
			continue // 跳过解析失败的记录
		}

		// 计算选项交集
		intersectionCount := s.calculateOptionsIntersection(userOptions, dbOptions)

		// 如果命中>=3个选项，则认为匹配
		if intersectionCount >= 3 {
			filtered = append(filtered, candidate)
		}
	}

	return filtered
}

// calculateOptionsIntersection 计算选项交集数量
func (s *MatchService) calculateOptionsIntersection(userOptions, dbOptions map[string]string) int {
	intersectionCount := 0

	for key, userValue := range userOptions {
		if dbValue, exists := dbOptions[key]; exists && userValue == dbValue {
			intersectionCount++
		}
	}

	return intersectionCount
}

// abs 计算绝对值
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}
