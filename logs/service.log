
2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.024ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.481ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.680ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND table_type = 'BASE TABLE'

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.396ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[61.984ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[57.001ms] [34;1m[rows:-][0m SELECT * FROM `hook_user` LIMIT 1

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.019ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_user' ORDER BY ORDINAL_POSITION

2025/06/16 11:58:32 [32m
[0m[33m[29.805ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:32 [32m
[0m[33m[59.402ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.248ms] [34;1m[rows:2][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_user' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/16 11:58:32 [32m
[0m[33m[29.296ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:32 [32m
[0m[33m[65.807ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:32 [32m
[0m[33m[63.516ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_user' AND constraint_name = 'uni_hook_user_phone'

2025/06/16 11:58:32 [32m
[0m[33m[29.704ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:32 [32m
[0m[33m[59.939ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:32 [32m
[0m[33m[60.486ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND index_name = 'idx_hook_user_phone'

2025/06/16 11:58:32 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.408ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.999ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[68.352ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND index_name = 'idx_hook_user_phone'

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.944ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.074ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.284ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND table_type = 'BASE TABLE'

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.444ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[58.592ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.129ms] [34;1m[rows:-][0m SELECT * FROM `hook_apps` LIMIT 1

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.759ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' ORDER BY ORDINAL_POSITION

2025/06/16 11:58:33 [32m
[0m[33m[29.824ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:33 [32m
[0m[33m[59.190ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.924ms] [34;1m[rows:3][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_apps' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/16 11:58:33 [32m
[0m[33m[30.571ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:33 [32m
[0m[33m[59.230ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:33 [32m
[0m[33m[59.080ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_apps' AND constraint_name = 'uni_hook_apps_app_id'

2025/06/16 11:58:33 [32m
[0m[33m[30.159ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:33 [32m
[0m[33m[59.567ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:33 [32m
[0m[33m[59.662ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_app_id'

2025/06/16 11:58:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.887ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.693ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.631ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_user_id'

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.541ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.057ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.477ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_app_id'

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.439ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.319ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[61.489ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_balance_logs' AND table_type = 'BASE TABLE'

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.710ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[57.370ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[58.664ms] [34;1m[rows:-][0m SELECT * FROM `hook_balance_logs` LIMIT 1

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.697ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_balance_logs' ORDER BY ORDINAL_POSITION

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.057ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.108ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.740ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_balance_logs' AND index_name = 'idx_hook_balance_logs_user_id'

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.464ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.887ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.008ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' AND table_type = 'BASE TABLE'

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.215ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:34 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.586ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:35 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.054ms] [34;1m[rows:-][0m SELECT * FROM `hook_system_config` LIMIT 1

2025/06/16 11:58:35 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[62.344ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' ORDER BY ORDINAL_POSITION

2025/06/16 11:58:35 [32m
[0m[33m[27.346ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:35 [32m
[0m[33m[59.983ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:35 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.084ms] [34;1m[rows:2][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_system_config' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/16 11:58:35 [32m
[0m[33m[29.982ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:35 [32m
[0m[33m[59.316ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:35 [32m
[0m[33m[60.144ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_system_config' AND constraint_name = 'uni_hook_system_config_key'

2025/06/16 11:58:35 [32m
[0m[33m[29.986ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:35 [32m
[0m[33m[59.387ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:35 [32m
[0m[33m[60.207ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' AND index_name = 'idx_hook_system_config_key'

2025/06/16 11:58:35 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.903ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:35 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.332ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:35 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.910ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' AND index_name = 'idx_hook_system_config_key'

2025/06/16 11:58:35 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.021ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:35 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.912ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:35 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[61.867ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_models' AND table_type = 'BASE TABLE'

2025/06/16 11:58:35 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[27.010ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:35 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.284ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:35 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.469ms] [34;1m[rows:-][0m SELECT * FROM `hook_models` LIMIT 1

2025/06/16 11:58:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[61.186ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_models' ORDER BY ORDINAL_POSITION

2025/06/16 11:58:36 [32m
[0m[33m[28.757ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:36 [32m
[0m[33m[60.277ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.845ms] [34;1m[rows:2][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_models' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/16 11:58:36 [32m
[0m[33m[29.200ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:36 [32m
[0m[33m[60.192ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:36 [32m
[0m[33m[59.499ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_models' AND constraint_name = 'uni_hook_models_model_name'

2025/06/16 11:58:36 [32m
[0m[33m[30.137ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:36 [32m
[0m[33m[59.701ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:36 [32m
[0m[33m[59.693ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_models' AND index_name = 'idx_hook_models_model_name'

2025/06/16 11:58:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[38.254ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `temperature` decimal(3,2) DEFAULT 0.7 COMMENT '温度参数'

2025/06/16 11:58:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[39.989ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `top_p` decimal(3,2) DEFAULT 0.9 COMMENT 'TopP参数'

2025/06/16 11:58:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[39.420ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `repetition_penalty` decimal(3,2) DEFAULT 1 COMMENT '重复惩罚'

2025/06/16 11:58:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[39.995ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `presence_penalty` decimal(3,2) DEFAULT 0 COMMENT '存在惩罚'

2025/06/16 11:58:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.656ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.200ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.669ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_models' AND index_name = 'idx_hook_models_model_name'

2025/06/16 11:58:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[31.365ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.230ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[58.363ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_question_bank' AND table_type = 'BASE TABLE'

2025/06/16 11:58:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.887ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.520ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.327ms] [34;1m[rows:-][0m SELECT * FROM `hook_question_bank` LIMIT 1

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.785ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_question_bank' ORDER BY ORDINAL_POSITION

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[28.676ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.928ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.452ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_question_bank' AND index_name = 'idx_hook_question_bank_hash_key'

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.217ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.619ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.608ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_solve_logs' AND table_type = 'BASE TABLE'

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.831ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.487ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.519ms] [34;1m[rows:-][0m SELECT * FROM `hook_solve_logs` LIMIT 1

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.857ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_solve_logs' ORDER BY ORDINAL_POSITION

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[29.322ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[59.368ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:47
[0m[33m[60.241ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_solve_logs' AND index_name = 'idx_hook_solve_logs_app_id'

2025/06/16 11:58:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/database/mysql.go:67
[0m[33m[59.662ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_user` WHERE role IN ('admin','manager')
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> solve-go-api/internal/router.Setup.func1 (6 handlers)
[GIN-debug] POST   /api/v1/send-sms          --> solve-go-api/internal/handlers.(*AuthHandler).SendSMSCode-fm (6 handlers)
[GIN-debug] POST   /api/v1/register          --> solve-go-api/internal/handlers.(*AuthHandler).Register-fm (6 handlers)
[GIN-debug] POST   /api/v1/login             --> solve-go-api/internal/handlers.(*AuthHandler).Login-fm (6 handlers)
[GIN-debug] POST   /api/v1/forgot-password   --> solve-go-api/internal/handlers.(*AuthHandler).ForgotPassword-fm (6 handlers)
[GIN-debug] POST   /api/v1/reset-password    --> solve-go-api/internal/handlers.(*AuthHandler).ResetPassword-fm (6 handlers)
[GIN-debug] POST   /api/v1/solve/question    --> solve-go-api/internal/handlers.(*SolveHandler).SolveQuestion-fm (7 handlers)
[GIN-debug] GET    /api/v1/user/profile      --> solve-go-api/internal/handlers.(*AuthHandler).GetProfile-fm (7 handlers)
[GIN-debug] PUT    /api/v1/user/profile      --> solve-go-api/internal/handlers.(*AuthHandler).UpdateProfile-fm (7 handlers)
[GIN-debug] POST   /api/v1/user/change-password --> solve-go-api/internal/handlers.(*AuthHandler).ChangePassword-fm (7 handlers)
[GIN-debug] GET    /api/v1/user/balance-logs --> solve-go-api/internal/handlers.(*AuthHandler).GetBalanceLogs-fm (7 handlers)
[GIN-debug] GET    /api/v1/apps/             --> solve-go-api/internal/handlers.(*AppHandler).GetApps-fm (7 handlers)
[GIN-debug] POST   /api/v1/apps/             --> solve-go-api/internal/handlers.(*AppHandler).CreateApp-fm (7 handlers)
[GIN-debug] PUT    /api/v1/apps/:id          --> solve-go-api/internal/handlers.(*AppHandler).UpdateApp-fm (7 handlers)
[GIN-debug] DELETE /api/v1/apps/:id          --> solve-go-api/internal/handlers.(*AppHandler).DeleteApp-fm (7 handlers)
[GIN-debug] POST   /api/v1/apps/:id/reset-secret --> solve-go-api/internal/handlers.(*AppHandler).ResetSecret-fm (7 handlers)
[GIN-debug] GET    /api/v1/apps/:id/logs     --> solve-go-api/internal/handlers.(*AppHandler).GetAppLogs-fm (7 handlers)
[GIN-debug] GET    /api/v1/manager/questions/ --> solve-go-api/internal/handlers.(*QuestionHandler).GetQuestions-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/questions/:id --> solve-go-api/internal/handlers.(*QuestionHandler).GetQuestion-fm (8 handlers)
[GIN-debug] PUT    /api/v1/manager/questions/:id --> solve-go-api/internal/handlers.(*QuestionHandler).UpdateQuestion-fm (8 handlers)
[GIN-debug] DELETE /api/v1/manager/questions/:id --> solve-go-api/internal/handlers.(*QuestionHandler).DeleteQuestion-fm (8 handlers)
[GIN-debug] POST   /api/v1/manager/questions/:id/verify --> solve-go-api/internal/handlers.(*QuestionHandler).VerifyQuestion-fm (8 handlers)
[GIN-debug] POST   /api/v1/manager/questions/:id/unverify --> solve-go-api/internal/handlers.(*QuestionHandler).UnverifyQuestion-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/questions/stats --> solve-go-api/internal/handlers.(*QuestionHandler).GetStats-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/logs/     --> solve-go-api/internal/handlers.(*LogHandler).GetLogs-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/logs/:id  --> solve-go-api/internal/handlers.(*LogHandler).GetLog-fm (8 handlers)
[GIN-debug] DELETE /api/v1/manager/logs/:id  --> solve-go-api/internal/handlers.(*LogHandler).DeleteLog-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/logs/stats --> solve-go-api/internal/handlers.(*LogHandler).GetStats-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/      --> solve-go-api/internal/handlers.(*AdminHandler).GetUsers-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/:id   --> solve-go-api/internal/handlers.(*AdminHandler).GetUser-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/users/:id   --> solve-go-api/internal/handlers.(*AdminHandler).UpdateUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/activate --> solve-go-api/internal/handlers.(*AdminHandler).ActivateUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/deactivate --> solve-go-api/internal/handlers.(*AdminHandler).DeactivateUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/recharge --> solve-go-api/internal/handlers.(*AdminHandler).RechargeUser-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/stats --> solve-go-api/internal/handlers.(*AdminHandler).GetUserStats-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/apps/       --> solve-go-api/internal/handlers.(*AdminHandler).GetAllApps-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/apps/:id    --> solve-go-api/internal/handlers.(*AdminHandler).UpdateApp-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/apps/:id/activate --> solve-go-api/internal/handlers.(*AdminHandler).ActivateApp-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/apps/:id/deactivate --> solve-go-api/internal/handlers.(*AdminHandler).DeactivateApp-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/config/     --> solve-go-api/internal/handlers.(*AdminHandler).GetConfigs-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/config/:key --> solve-go-api/internal/handlers.(*AdminHandler).UpdateConfig-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/models/     --> solve-go-api/internal/handlers.(*AdminHandler).GetModels-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/models/     --> solve-go-api/internal/handlers.(*AdminHandler).CreateModel-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/models/:id  --> solve-go-api/internal/handlers.(*AdminHandler).UpdateModel-fm (8 handlers)
[GIN-debug] DELETE /api/v1/admin/models/:id  --> solve-go-api/internal/handlers.(*AdminHandler).DeleteModel-fm (8 handlers)
2025/06/16 11:58:37 Server starting on port 8080
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
[GIN-debug] Listening and serving HTTP on :8080
time="2025-06-16T11:58:42+08:00" level=info msg="HTTP Request" body_size=15 client_ip="::1" error= latency="26.167µs" method=GET path=/health status_code=200 timestamp="2025-06-16T11:58:42+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 11:58:42 | 200 |      86.333µs |             ::1 | GET      "/health"
[GIN] 2025/06/16 - 12:00:59 | 204 |          46µs |             ::1 | OPTIONS  "/api/v1/login"

2025/06/16 12:00:59 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[61.470ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000001' ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:00:59+08:00" level=info msg="HTTP Request" body_size=37 client_ip="::1" error= latency=125.06825ms method=POST path=/api/v1/login status_code=401 timestamp="2025-06-16T12:00:59+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:00:59 | 401 |  125.116958ms |             ::1 | POST     "/api/v1/login"
[GIN] 2025/06/16 - 12:01:10 | 204 |      51.041µs |             ::1 | OPTIONS  "/api/v1/login"

2025/06/16 12:01:10 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[68.704ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000001' ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:01:10+08:00" level=info msg="HTTP Request" body_size=37 client_ip="::1" error= latency=145.696416ms method=POST path=/api/v1/login status_code=401 timestamp="2025-06-16T12:01:10+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:01:10 | 401 |  145.808291ms |             ::1 | POST     "/api/v1/login"
time="2025-06-16T12:01:33+08:00" level=info msg="HTTP Request" body_size=15 client_ip="::1" error= latency="18.75µs" method=GET path=/health status_code=200 timestamp="2025-06-16T12:01:33+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:01:33 | 200 |      72.667µs |             ::1 | GET      "/health"

2025/06/16 12:01:42 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[65.067ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000001' ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:01:42+08:00" level=info msg="HTTP Request" body_size=37 client_ip="::1" error= latency=128.451375ms method=POST path=/api/v1/login status_code=401 timestamp="2025-06-16T12:01:42+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:01:42 | 401 |  128.633875ms |             ::1 | POST     "/api/v1/login"
time="2025-06-16T12:02:16+08:00" level=info msg="HTTP Request" body_size=-1 client_ip="::1" error= latency="7.333µs" method=GET path=/api/health status_code=404 timestamp="2025-06-16T12:02:16+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:02:16 | 404 |      65.625µs |             ::1 | GET      "/api/health"

2025/06/16 12:02:16 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[62.808ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000001' ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:02:16+08:00" level=info msg="HTTP Request" body_size=37 client_ip="::1" error= latency=115.748541ms method=POST path=/api/v1/login status_code=401 timestamp="2025-06-16T12:02:16+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:02:16 | 401 |  115.807666ms |             ::1 | POST     "/api/v1/login"

2025/06/16 12:02:16 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[64.064ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000002' ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:02:16+08:00" level=info msg="HTTP Request" body_size=37 client_ip="::1" error= latency=118.893583ms method=POST path=/api/v1/login status_code=401 timestamp="2025-06-16T12:02:16+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:02:16 | 401 |    118.9875ms |             ::1 | POST     "/api/v1/login"

2025/06/16 12:02:16 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[63.351ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000003' ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:02:16+08:00" level=info msg="HTTP Request" body_size=37 client_ip="::1" error= latency=115.285958ms method=POST path=/api/v1/login status_code=401 timestamp="2025-06-16T12:02:16+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:02:16 | 401 |  115.356208ms |             ::1 | POST     "/api/v1/login"

2025/06/16 12:05:28 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[58.789ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000001' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:05:28 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[127.296ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 1
time="2025-06-16T12:05:28+08:00" level=info msg="HTTP Request" body_size=366 client_ip="::1" error= latency=249.200708ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:05:28+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:05:28 | 200 |  249.257083ms |             ::1 | POST     "/api/v1/login"
[GIN] 2025/06/16 - 12:05:43 | 204 |      44.875µs |             ::1 | OPTIONS  "/api/v1/login"

2025/06/16 12:05:43 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[73.388ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000001' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:05:43 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[141.732ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 1
time="2025-06-16T12:05:43+08:00" level=info msg="HTTP Request" body_size=366 client_ip="::1" error= latency=293.418375ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:05:43+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:05:43 | 200 |  293.512875ms |             ::1 | POST     "/api/v1/login"
[GIN] 2025/06/16 - 12:05:43 | 204 |      15.166µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:05:43 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[58.820ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 1 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:05:43+08:00" level=info msg="HTTP Request" body_size=229 client_ip="::1" error= latency=59.369875ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:05:43+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:05:43 | 200 |     59.4915ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:05:43 | 204 |       6.417µs |             ::1 | OPTIONS  "/api/v1/apps"
[GIN-debug] redirecting request 301: /api/v1/apps/ --> /api/v1/apps/
time="2025-06-16T12:06:05+08:00" level=info msg="HTTP Request" body_size=-1 client_ip="::1" error= latency="8.458µs" method=GET path=/api/health status_code=404 timestamp="2025-06-16T12:06:05+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:06:05 | 404 |      79.959µs |             ::1 | GET      "/api/health"

2025/06/16 12:06:05 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[82.781ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000001' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:06:05 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[135.004ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 1
time="2025-06-16T12:06:05+08:00" level=info msg="HTTP Request" body_size=366 client_ip="::1" error= latency=312.215666ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:06:05+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:06:05 | 200 |    312.2525ms |             ::1 | POST     "/api/v1/login"

2025/06/16 12:06:05 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[57.038ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000002' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:06:05 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[133.724ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 2
time="2025-06-16T12:06:05+08:00" level=info msg="HTTP Request" body_size=371 client_ip="::1" error= latency=258.452458ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:06:05+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:06:05 | 200 |   258.49075ms |             ::1 | POST     "/api/v1/login"

2025/06/16 12:06:05 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[63.350ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000003' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:06:06 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[133.462ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 3
time="2025-06-16T12:06:06+08:00" level=info msg="HTTP Request" body_size=371 client_ip="::1" error= latency=266.692625ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:06:06+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:06:06 | 200 |  266.735916ms |             ::1 | POST     "/api/v1/login"
time="2025-06-16T12:06:21+08:00" level=info msg="HTTP Request" body_size=15 client_ip="::1" error= latency="19.875µs" method=GET path=/health status_code=200 timestamp="2025-06-16T12:06:21+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:06:21 | 200 |      69.334µs |             ::1 | GET      "/health"

2025/06/16 12:06:27 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[60.675ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000001' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:06:28 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[126.932ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 1
time="2025-06-16T12:06:28+08:00" level=info msg="HTTP Request" body_size=366 client_ip="::1" error= latency=250.930292ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:06:28+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:06:28 | 200 |  250.984958ms |             ::1 | POST     "/api/v1/login"

2025/06/16 12:07:00 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[63.865ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000001' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:07:00 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[128.987ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 1
time="2025-06-16T12:07:00+08:00" level=info msg="HTTP Request" body_size=366 client_ip="::1" error= latency=254.097542ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:07:00+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:07:00 | 200 |  254.138917ms |             ::1 | POST     "/api/v1/login"
[GIN] 2025/06/16 - 12:09:45 | 204 |      36.708µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:09:45 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[65.215ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 1 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:09:45+08:00" level=info msg="HTTP Request" body_size=229 client_ip="::1" error= latency=65.826125ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:09:45+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:09:45 | 200 |   65.973417ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:09:46 | 204 |        11.5µs |             ::1 | OPTIONS  "/api/v1/apps"

2025/06/16 12:09:47 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[60.607ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 1 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:09:47+08:00" level=info msg="HTTP Request" body_size=229 client_ip="::1" error= latency=60.72975ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:09:47+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:09:47 | 200 |   60.763375ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:09:51 | 204 |       6.917µs |             ::1 | OPTIONS  "/api/v1/user/balance-logs?page=1&page_size=10"

2025/06/16 12:09:51 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[66.514ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 1

2025/06/16 12:09:51 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[70.154ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 1 ORDER BY created_at DESC LIMIT 10
time="2025-06-16T12:09:51+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=137.177959ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=10" status_code=500 timestamp="2025-06-16T12:09:51+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:09:51 | 500 |  137.299958ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=10"
[GIN] 2025/06/16 - 12:09:54 | 204 |       9.125µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:09:54 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[60.631ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 1 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:09:54+08:00" level=info msg="HTTP Request" body_size=229 client_ip="::1" error= latency=60.947042ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:09:54+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:09:54 | 200 |   61.041917ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:09:54 | 204 |       6.958µs |             ::1 | OPTIONS  "/api/v1/apps"

2025/06/16 12:09:55 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[70.939ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 1 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:09:55+08:00" level=info msg="HTTP Request" body_size=229 client_ip="::1" error= latency=71.36525ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:09:55+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:09:55 | 200 |    71.53175ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:10:00 | 204 |       6.291µs |             ::1 | OPTIONS  "/api/v1/apps"
[GIN] 2025/06/16 - 12:10:03 | 204 |     468.708µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:10:03 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[61.293ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 1 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:10:03+08:00" level=info msg="HTTP Request" body_size=229 client_ip="::1" error= latency=61.637334ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:10:03+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:10:03 | 200 |   61.723958ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:10:16 | 204 |      14.917µs |             ::1 | OPTIONS  "/api/v1/user/balance-logs?page=1&page_size=10"

2025/06/16 12:10:16 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[62.011ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 1

2025/06/16 12:10:16 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[60.277ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 1 ORDER BY created_at DESC LIMIT 10
time="2025-06-16T12:10:16+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=122.778708ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=10" status_code=500 timestamp="2025-06-16T12:10:16+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:10:16 | 500 |  122.985959ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=10"
[GIN] 2025/06/16 - 12:11:29 | 204 |      37.125µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:11:29 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[64.723ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 1 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:11:29+08:00" level=info msg="HTTP Request" body_size=229 client_ip="::1" error= latency=65.076291ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:11:29+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:11:29 | 200 |   65.180083ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:11:29 | 204 |       9.292µs |             ::1 | OPTIONS  "/api/v1/apps"
[GIN] 2025/06/16 - 12:11:39 | 204 |      11.334µs |             ::1 | OPTIONS  "/api/v1/login"

2025/06/16 12:11:39 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[66.796ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000001' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:11:40 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[131.601ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 1
time="2025-06-16T12:11:40+08:00" level=info msg="HTTP Request" body_size=366 client_ip="::1" error= latency=275.257584ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:11:40+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:11:40 | 200 |  275.373083ms |             ::1 | POST     "/api/v1/login"
[GIN] 2025/06/16 - 12:11:40 | 204 |       9.667µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:11:40 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[59.685ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 1 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:11:40+08:00" level=info msg="HTTP Request" body_size=229 client_ip="::1" error= latency=60.257792ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:11:40+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:11:40 | 200 |   60.443125ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:11:40 | 204 |      12.917µs |             ::1 | OPTIONS  "/api/v1/apps"
[GIN] 2025/06/16 - 12:12:07 | 204 |      16.084µs |             ::1 | OPTIONS  "/api/v1/send-sms"
time="2025-06-16T12:12:07+08:00" level=info msg="HTTP Request" body_size=71 client_ip="::1" error= latency=510.435667ms method=POST path=/api/v1/send-sms status_code=200 timestamp="2025-06-16T12:12:07+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:12:07 | 200 |  510.647625ms |             ::1 | POST     "/api/v1/send-sms"
[GIN] 2025/06/16 - 12:12:15 | 204 |      11.375µs |             ::1 | OPTIONS  "/api/v1/register"

2025/06/16 12:12:15 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:25
[0m[33m[60.113ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_user` WHERE phone = '15653259315'

2025/06/16 12:12:15 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:45
[0m[33m[141.432ms] [34;1m[rows:1][0m INSERT INTO `hook_user` (`phone`,`password`,`role`,`nickname`,`balance`,`is_active`,`created_at`,`updated_at`) VALUES ('15653259315','$2a$10$qvu00G7rBdV6mD7.I5aCCu/WtAdgW6evqoqRpWJ6zQb67Z7fphJv6','user','果沐云计算',0,true,'2025-06-16 12:12:15.425','2025-06-16 12:12:15.425')
time="2025-06-16T12:12:15+08:00" level=info msg="HTTP Request" body_size=137 client_ip="::1" error= latency=358.787458ms method=POST path=/api/v1/register status_code=200 timestamp="2025-06-16T12:12:15+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:12:15 | 200 |  358.902917ms |             ::1 | POST     "/api/v1/register"
[GIN] 2025/06/16 - 12:12:20 | 204 |      12.084µs |             ::1 | OPTIONS  "/api/v1/login"

2025/06/16 12:12:20 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[63.791ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13800000001' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:12:20 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[134.150ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 1
time="2025-06-16T12:12:20+08:00" level=info msg="HTTP Request" body_size=366 client_ip="::1" error= latency=274.602334ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:12:20+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:12:20 | 200 |  274.734167ms |             ::1 | POST     "/api/v1/login"
[GIN] 2025/06/16 - 12:12:20 | 204 |       5.417µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:12:20 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[61.871ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 1 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:12:20+08:00" level=info msg="HTTP Request" body_size=229 client_ip="::1" error= latency=62.187625ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:12:20+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:12:20 | 200 |   62.306834ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:12:20 | 204 |       7.333µs |             ::1 | OPTIONS  "/api/v1/apps"
[GIN] 2025/06/16 - 12:12:25 | 204 |       11.75µs |             ::1 | OPTIONS  "/api/v1/user/balance-logs?page=1&page_size=10"

2025/06/16 12:12:25 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[65.877ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 1

2025/06/16 12:12:25 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[60.176ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 1 ORDER BY created_at DESC LIMIT 10
time="2025-06-16T12:12:25+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=126.549291ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=10" status_code=500 timestamp="2025-06-16T12:12:25+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:12:25 | 500 |  126.628792ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=10"
[GIN] 2025/06/16 - 12:12:27 | 204 |       8.125µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:12:27 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[57.696ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 1 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:12:27+08:00" level=info msg="HTTP Request" body_size=229 client_ip="::1" error= latency=58.06325ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:12:27+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:12:27 | 200 |   58.174541ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:12:27 | 204 |       6.708µs |             ::1 | OPTIONS  "/api/v1/apps"

2025/06/16 12:12:28 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[61.095ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 1 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:12:28+08:00" level=info msg="HTTP Request" body_size=229 client_ip="::1" error= latency=61.496958ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:12:28+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:12:28 | 200 |   61.646792ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:12:59 | 204 |      43.958µs |             ::1 | OPTIONS  "/api/v1/login"

2025/06/16 12:13:00 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[134.937ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '15653259315' ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:13:00+08:00" level=info msg="HTTP Request" body_size=43 client_ip="::1" error= latency=212.1365ms method=POST path=/api/v1/login status_code=401 timestamp="2025-06-16T12:13:00+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:13:00 | 401 |  212.200542ms |             ::1 | POST     "/api/v1/login"
[GIN] 2025/06/16 - 12:13:12 | 204 |      10.083µs |             ::1 | OPTIONS  "/api/v1/login"

2025/06/16 12:13:12 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[61.729ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '15653259315' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:13:13 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[133.713ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 5
time="2025-06-16T12:13:13+08:00" level=info msg="HTTP Request" body_size=363 client_ip="::1" error= latency=271.694792ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:13:13+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:13:13 | 200 |  271.876542ms |             ::1 | POST     "/api/v1/login"
[GIN] 2025/06/16 - 12:13:13 | 204 |       11.75µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:13:13 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[59.010ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:13:13+08:00" level=info msg="HTTP Request" body_size=228 client_ip="::1" error= latency=59.342167ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:13:13+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:13:13 | 200 |   59.494083ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:13:13 | 204 |      12.416µs |             ::1 | OPTIONS  "/api/v1/apps"
[GIN] 2025/06/16 - 12:13:24 | 204 |       6.625µs |             ::1 | OPTIONS  "/api/v1/apps"
[GIN] 2025/06/16 - 12:13:34 | 204 |          16µs |             ::1 | OPTIONS  "/api/v1/apps"
[GIN-debug] redirecting request 307: /api/v1/apps/ --> /api/v1/apps/

2025/06/16 12:16:03 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[65.511ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '15653259315' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:16:03 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[135.681ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 5
time="2025-06-16T12:16:03+08:00" level=info msg="HTTP Request" body_size=363 client_ip="::1" error= latency=255.327167ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:16:03+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:16:03 | 200 |  255.369458ms |             ::1 | POST     "/api/v1/login"
time="2025-06-16T12:16:26+08:00" level=info msg="HTTP Request" body_size=15 client_ip="::1" error= latency="25.833µs" method=GET path=/health status_code=200 timestamp="2025-06-16T12:16:26+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:16:26 | 200 |      77.209µs |             ::1 | GET      "/health"

2025/06/16 12:16:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[68.045ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '15653259315' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:16:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[133.405ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 5
time="2025-06-16T12:16:33+08:00" level=info msg="HTTP Request" body_size=363 client_ip="::1" error= latency=264.550917ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:16:33+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:16:33 | 200 |  264.601167ms |             ::1 | POST     "/api/v1/login"

2025/06/16 12:17:39 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[56.970ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '15653259315' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:17:39 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[135.570ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 5
time="2025-06-16T12:17:39+08:00" level=info msg="HTTP Request" body_size=363 client_ip="::1" error= latency=257.785ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:17:39+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:17:39 | 200 |  257.853125ms |             ::1 | POST     "/api/v1/login"
[GIN] 2025/06/16 - 12:18:13 | 204 |      37.083µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:18:13 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[69.807ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:18:13+08:00" level=info msg="HTTP Request" body_size=228 client_ip="::1" error= latency=70.118541ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:18:13+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:18:13 | 200 |   70.235625ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:18:13 | 204 |       8.792µs |             ::1 | OPTIONS  "/api/v1/apps"

2025/06/16 12:18:14 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[60.349ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:18:14+08:00" level=info msg="HTTP Request" body_size=228 client_ip="::1" error= latency=60.634084ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:18:14+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:18:14 | 200 |   60.715416ms |             ::1 | GET      "/api/v1/user/profile"

2025/06/16 12:18:20 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[65.071ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '15653259315' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:18:21 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[129.200ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 5
time="2025-06-16T12:18:21+08:00" level=info msg="HTTP Request" body_size=363 client_ip="::1" error= latency=265.070375ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:18:21+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:18:21 | 200 |  265.101708ms |             ::1 | POST     "/api/v1/login"
[GIN-debug] redirecting request 307: /api/v1/apps/ --> /api/v1/apps/
time="2025-06-16T12:20:16+08:00" level=info msg="HTTP Request" body_size=-1 client_ip="::1" error= latency="10.083µs" method=GET path=/api/health status_code=404 timestamp="2025-06-16T12:20:16+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:20:16 | 404 |     109.291µs |             ::1 | GET      "/api/health"
time="2025-06-16T12:20:37+08:00" level=info msg="HTTP Request" body_size=15 client_ip="::1" error= latency="21.458µs" method=GET path=/health status_code=200 timestamp="2025-06-16T12:20:37+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:20:37 | 200 |      84.167µs |             ::1 | GET      "/health"

2025/06/16 12:20:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[63.083ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '15653259315' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:20:38 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[135.259ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 5
time="2025-06-16T12:20:38+08:00" level=info msg="HTTP Request" body_size=363 client_ip="::1" error= latency=263.235375ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:20:38+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:20:38 | 200 |  263.267125ms |             ::1 | POST     "/api/v1/login"

2025/06/16 12:20:38 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[60.671ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:20:38+08:00" level=info msg="HTTP Request" body_size=228 client_ip="::1" error= latency=60.804834ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:20:38+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:20:38 | 200 |   60.839375ms |             ::1 | GET      "/api/v1/user/profile"
[GIN-debug] redirecting request 301: /api/v1/apps/ --> /api/v1/apps/
time="2025-06-16T12:21:37+08:00" level=info msg="HTTP Request" body_size=15 client_ip="::1" error= latency="19.958µs" method=GET path=/health status_code=200 timestamp="2025-06-16T12:21:37+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:21:37 | 200 |      72.292µs |             ::1 | GET      "/health"

2025/06/16 12:21:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[56.506ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '15653259315' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:21:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[131.029ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 5
time="2025-06-16T12:21:37+08:00" level=info msg="HTTP Request" body_size=363 client_ip="::1" error= latency=246.6105ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:21:37+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:21:37 | 200 |  246.642042ms |             ::1 | POST     "/api/v1/login"

2025/06/16 12:21:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[57.806ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:21:37+08:00" level=info msg="HTTP Request" body_size=228 client_ip="::1" error= latency=57.96075ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:21:37+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:21:37 | 200 |   57.991542ms |             ::1 | GET      "/api/v1/user/profile"

2025/06/16 12:21:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[65.564ms] [34;1m[rows:0][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:21:37+08:00" level=info msg="HTTP Request" body_size=42 client_ip="::1" error= latency=65.796333ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:21:37+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:21:37 | 200 |    65.84125ms |             ::1 | GET      "/api/v1/apps/"

2025/06/16 12:21:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:175
[0m[33m[61.628ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_apps` WHERE app_id = 'PvpKuwyCBxUmnvBG'

2025/06/16 12:21:38 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:42
[0m[33m[129.839ms] [34;1m[rows:1][0m INSERT INTO `hook_apps` (`user_id`,`name`,`app_id`,`app_secret`,`status`,`total_calls`,`created_at`) VALUES (5,'学法减分','PvpKuwyCBxUmnvBG','FNKdsTcZSHrKJIMCtK3sDbfOQHBGnbFN',0,0,'2025-06-16 12:21:37.997')
time="2025-06-16T12:21:38+08:00" level=info msg="HTTP Request" body_size=394 client_ip="::1" error= latency=191.829833ms method=POST path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:21:38+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:21:38 | 200 |  191.900791ms |             ::1 | POST     "/api/v1/apps/"

2025/06/16 12:21:38 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[58.248ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:21:38+08:00" level=info msg="HTTP Request" body_size=385 client_ip="::1" error= latency=58.380292ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:21:38+08:00" user_agent=curl/8.7.1
[GIN] 2025/06/16 - 12:21:38 | 200 |   58.411334ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:27:32 | 204 |      39.708µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:27:33 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[62.956ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:27:33+08:00" level=info msg="HTTP Request" body_size=228 client_ip="::1" error= latency=63.322209ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:27:33+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:27:33 | 200 |   63.450208ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:27:33 | 204 |       8.709µs |             ::1 | OPTIONS  "/api/v1/apps"
[GIN] 2025/06/16 - 12:27:39 | 204 |      10.208µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:27:39 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[65.333ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:27:39+08:00" level=info msg="HTTP Request" body_size=228 client_ip="::1" error= latency=65.608375ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:27:39+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:27:39 | 200 |   65.710292ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:27:39 | 204 |        8.25µs |             ::1 | OPTIONS  "/api/v1/apps"
[GIN] 2025/06/16 - 12:31:34 | 204 |      43.708µs |             ::1 | OPTIONS  "/api/v1/apps/"
time="2025-06-16T12:31:34+08:00" level=info msg="HTTP Request" body_size=46 client_ip="::1" error= latency="40.375µs" method=GET path=/api/v1/apps/ status_code=401 timestamp="2025-06-16T12:31:34+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:31:34 | 401 |     162.208µs |             ::1 | GET      "/api/v1/apps/"
time="2025-06-16T12:31:36+08:00" level=info msg="HTTP Request" body_size=46 client_ip="::1" error= latency="23.042µs" method=POST path=/api/v1/apps/ status_code=401 timestamp="2025-06-16T12:31:36+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:31:36 | 401 |     105.459µs |             ::1 | POST     "/api/v1/apps/"
[GIN] 2025/06/16 - 12:31:41 | 204 |      10.458µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:31:41 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[63.701ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:31:41+08:00" level=info msg="HTTP Request" body_size=228 client_ip="::1" error= latency=64.072041ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:31:41+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:31:41 | 200 |   64.184375ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:31:41 | 204 |       9.125µs |             ::1 | OPTIONS  "/api/v1/apps/"

2025/06/16 12:31:41 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[61.778ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:31:41+08:00" level=info msg="HTTP Request" body_size=385 client_ip="::1" error= latency=62.071333ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:31:41+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:31:41 | 200 |   62.252833ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:31:41 | 204 |       8.333µs |             ::1 | OPTIONS  "/api/v1/user/balance-logs?page=1&page_size=5"

2025/06/16 12:31:41 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[60.278ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 5

2025/06/16 12:31:41 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[59.148ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 5 ORDER BY created_at DESC LIMIT 5
time="2025-06-16T12:31:41+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=119.921542ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=5" status_code=500 timestamp="2025-06-16T12:31:41+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:31:41 | 500 |  119.970417ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=5"

2025/06/16 12:31:42 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[66.195ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:31:42+08:00" level=info msg="HTTP Request" body_size=228 client_ip="::1" error= latency=66.527208ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:31:42+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:31:42 | 200 |   66.652166ms |             ::1 | GET      "/api/v1/user/profile"

2025/06/16 12:31:42 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[64.718ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:31:42+08:00" level=info msg="HTTP Request" body_size=385 client_ip="::1" error= latency=64.997625ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:31:42+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:31:42 | 200 |   65.096875ms |             ::1 | GET      "/api/v1/apps/"

2025/06/16 12:31:42 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[65.446ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 5

2025/06/16 12:31:42 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[59.601ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 5 ORDER BY created_at DESC LIMIT 5
time="2025-06-16T12:31:42+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=125.38775ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=5" status_code=500 timestamp="2025-06-16T12:31:42+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:31:42 | 500 |  125.490791ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=5"

2025/06/16 12:31:43 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[60.539ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:31:43+08:00" level=info msg="HTTP Request" body_size=385 client_ip="::1" error= latency=60.9215ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:31:43+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:31:43 | 200 |   61.090375ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:31:59 | 204 |       9.958µs |             ::1 | OPTIONS  "/api/v1/user/balance-logs?page=1&page_size=10"

2025/06/16 12:31:59 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[64.635ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 5

2025/06/16 12:32:00 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[66.322ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 5 ORDER BY created_at DESC LIMIT 10
time="2025-06-16T12:32:00+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=131.475709ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=10" status_code=500 timestamp="2025-06-16T12:32:00+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:00 | 500 |     131.734ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=10"
[GIN] 2025/06/16 - 12:32:00 | 204 |       5.583µs |             ::1 | OPTIONS  "/api/v1/apps/"

2025/06/16 12:32:00 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[64.135ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:32:00+08:00" level=info msg="HTTP Request" body_size=385 client_ip="::1" error= latency=64.540209ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:32:00+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:00 | 200 |   64.703042ms |             ::1 | GET      "/api/v1/apps/"

2025/06/16 12:32:03 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[63.558ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 5

2025/06/16 12:32:03 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[59.415ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 5 ORDER BY created_at DESC LIMIT 10
time="2025-06-16T12:32:03+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=123.576292ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=10" status_code=500 timestamp="2025-06-16T12:32:03+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:03 | 500 |  123.850375ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=10"

2025/06/16 12:32:04 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[66.493ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:32:04+08:00" level=info msg="HTTP Request" body_size=385 client_ip="::1" error= latency=66.766375ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:32:04+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:04 | 200 |    66.91175ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:32:07 | 204 |      11.292µs |             ::1 | OPTIONS  "/api/v1/apps/"

2025/06/16 12:32:08 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[59.123ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:32:08+08:00" level=info msg="HTTP Request" body_size=385 client_ip="::1" error= latency=59.458334ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:32:08+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:08 | 200 |      59.716ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:32:15 | 204 |      90.333µs |             ::1 | OPTIONS  "/api/v1/apps/"

2025/06/16 12:32:15 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:175
[0m[33m[66.881ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_apps` WHERE app_id = '3Eh1gwcZn1BUWAYl'

2025/06/16 12:32:15 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:42
[0m[33m[129.990ms] [34;1m[rows:1][0m INSERT INTO `hook_apps` (`user_id`,`name`,`app_id`,`app_secret`,`status`,`total_calls`,`created_at`) VALUES (5,'拍照搜题','3Eh1gwcZn1BUWAYl','RNdefajrtdD0M93lSMZVoXD0UvEz0qdK',0,0,'2025-06-16 12:32:15.542')
time="2025-06-16T12:32:15+08:00" level=info msg="HTTP Request" body_size=394 client_ip="::1" error= latency=197.6035ms method=POST path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:32:15+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:15 | 200 |  197.792875ms |             ::1 | POST     "/api/v1/apps/"

2025/06/16 12:32:15 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[58.944ms] [34;1m[rows:2][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:32:15+08:00" level=info msg="HTTP Request" body_size=729 client_ip="::1" error= latency=59.30625ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:32:15+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:15 | 200 |   59.444333ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:32:23 | 204 |      11.917µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:32:23 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[66.422ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:32:23+08:00" level=info msg="HTTP Request" body_size=228 client_ip="::1" error= latency=66.842875ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:32:23+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:23 | 200 |   66.969916ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:32:36 | 204 |           8µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:32:36 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[58.122ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:32:36+08:00" level=info msg="HTTP Request" body_size=232 client_ip="::1" error= latency=58.372458ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:32:36+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:36 | 200 |   58.477125ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:32:36 | 204 |       7.875µs |             ::1 | OPTIONS  "/api/v1/apps/"

2025/06/16 12:32:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[61.889ms] [34;1m[rows:2][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:32:37+08:00" level=info msg="HTTP Request" body_size=729 client_ip="::1" error= latency=62.146084ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:32:37+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:37 | 200 |   62.299417ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:32:37 | 204 |       8.375µs |             ::1 | OPTIONS  "/api/v1/user/balance-logs?page=1&page_size=5"

2025/06/16 12:32:37 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[61.272ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 5

2025/06/16 12:32:37 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[59.902ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 5 ORDER BY created_at DESC LIMIT 5
time="2025-06-16T12:32:37+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=121.666208ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=5" status_code=500 timestamp="2025-06-16T12:32:37+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:37 | 500 |  121.888042ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=5"

2025/06/16 12:32:39 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[62.051ms] [34;1m[rows:2][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:32:39+08:00" level=info msg="HTTP Request" body_size=729 client_ip="::1" error= latency=62.319208ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:32:39+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:39 | 200 |   62.469334ms |             ::1 | GET      "/api/v1/apps/"

2025/06/16 12:32:41 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[64.966ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:32:41+08:00" level=info msg="HTTP Request" body_size=232 client_ip="::1" error= latency=65.36225ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:32:41+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:41 | 200 |   65.506708ms |             ::1 | GET      "/api/v1/user/profile"

2025/06/16 12:32:42 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[58.579ms] [34;1m[rows:2][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:32:42+08:00" level=info msg="HTTP Request" body_size=729 client_ip="::1" error= latency=58.922583ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:32:42+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:42 | 200 |   59.164292ms |             ::1 | GET      "/api/v1/apps/"

2025/06/16 12:32:42 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[57.244ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 5

2025/06/16 12:32:42 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[59.770ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 5 ORDER BY created_at DESC LIMIT 5
time="2025-06-16T12:32:42+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=117.699916ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=5" status_code=500 timestamp="2025-06-16T12:32:42+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:42 | 500 |  117.859333ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=5"
[GIN] 2025/06/16 - 12:32:43 | 204 |      11.416µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:32:43 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[69.928ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:32:43+08:00" level=info msg="HTTP Request" body_size=232 client_ip="::1" error= latency=70.334541ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:32:43+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:43 | 200 |   70.488542ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:32:46 | 204 |        11.5µs |             ::1 | OPTIONS  "/api/v1/apps/"

2025/06/16 12:32:46 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[66.260ms] [34;1m[rows:2][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:32:46+08:00" level=info msg="HTTP Request" body_size=729 client_ip="::1" error= latency=66.740834ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:32:46+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:46 | 200 |   66.868542ms |             ::1 | GET      "/api/v1/apps/"

2025/06/16 12:32:47 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[58.805ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:32:47+08:00" level=info msg="HTTP Request" body_size=232 client_ip="::1" error= latency=59.2165ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:32:47+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:47 | 200 |   59.410875ms |             ::1 | GET      "/api/v1/user/profile"

2025/06/16 12:32:48 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[63.073ms] [34;1m[rows:2][0m SELECT * FROM `hook_apps` WHERE user_id = 5
time="2025-06-16T12:32:48+08:00" level=info msg="HTTP Request" body_size=729 client_ip="::1" error= latency=63.468458ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:32:48+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:48 | 200 |   63.608292ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:32:49 | 204 |      13.417µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:32:49 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[65.592ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 5 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:32:49+08:00" level=info msg="HTTP Request" body_size=232 client_ip="::1" error= latency=66.128875ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:32:49+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:49 | 200 |   66.311334ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:32:52 | 204 |      14.875µs |             ::1 | OPTIONS  "/api/v1/user/balance-logs?page=1&page_size=10"

2025/06/16 12:32:52 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[68.209ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 5

2025/06/16 12:32:52 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[59.929ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 5 ORDER BY created_at DESC LIMIT 10
time="2025-06-16T12:32:52+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=128.821416ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=10" status_code=500 timestamp="2025-06-16T12:32:52+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:32:52 | 500 |  128.997334ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=10"
[GIN] 2025/06/16 - 12:53:05 | 204 |      31.708µs |             ::1 | OPTIONS  "/api/v1/send-sms"
time="2025-06-16T12:53:06+08:00" level=info msg="HTTP Request" body_size=71 client_ip="::1" error= latency=683.871958ms method=POST path=/api/v1/send-sms status_code=200 timestamp="2025-06-16T12:53:06+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:06 | 200 |  684.090209ms |             ::1 | POST     "/api/v1/send-sms"
[GIN] 2025/06/16 - 12:53:11 | 204 |       7.208µs |             ::1 | OPTIONS  "/api/v1/register"

2025/06/16 12:53:12 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:25
[0m[33m[60.021ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_user` WHERE phone = '15688515913'

2025/06/16 12:53:12 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:45
[0m[33m[131.626ms] [34;1m[rows:1][0m INSERT INTO `hook_user` (`phone`,`password`,`role`,`nickname`,`balance`,`is_active`,`created_at`,`updated_at`) VALUES ('15688515913','$2a$10$L/YA5vJ6ISNykr9IhhGFAOs6641aZeXGB4d4aJtdP8X/EeY4OFsFy','user','guomu',0,true,'2025-06-16 12:53:12.211','2025-06-16 12:53:12.211')
time="2025-06-16T12:53:12+08:00" level=info msg="HTTP Request" body_size=127 client_ip="::1" error= latency=347.572084ms method=POST path=/api/v1/register status_code=200 timestamp="2025-06-16T12:53:12+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:12 | 200 |  347.654875ms |             ::1 | POST     "/api/v1/register"
[GIN] 2025/06/16 - 12:53:20 | 204 |      18.708µs |             ::1 | OPTIONS  "/api/v1/login"

2025/06/16 12:53:20 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[67.993ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '15688515913' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:53:20 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[132.691ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 8
time="2025-06-16T12:53:20+08:00" level=info msg="HTTP Request" body_size=353 client_ip="::1" error= latency=278.014ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:53:20+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:20 | 200 |  278.140291ms |             ::1 | POST     "/api/v1/login"
[GIN] 2025/06/16 - 12:53:20 | 204 |      10.458µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:53:20 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[57.797ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 8 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:53:20+08:00" level=info msg="HTTP Request" body_size=218 client_ip="::1" error= latency=58.147292ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:53:20+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:20 | 200 |   58.320708ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:53:20 | 204 |       5.375µs |             ::1 | OPTIONS  "/api/v1/apps/"

2025/06/16 12:53:20 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[64.399ms] [34;1m[rows:0][0m SELECT * FROM `hook_apps` WHERE user_id = 8
time="2025-06-16T12:53:20+08:00" level=info msg="HTTP Request" body_size=42 client_ip="::1" error= latency=64.83975ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:53:20+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:20 | 200 |   65.015041ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:53:20 | 204 |       7.333µs |             ::1 | OPTIONS  "/api/v1/user/balance-logs?page=1&page_size=5"

2025/06/16 12:53:20 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[60.158ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 8

2025/06/16 12:53:20 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[59.737ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 8 ORDER BY created_at DESC LIMIT 5
time="2025-06-16T12:53:20+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=120.379709ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=5" status_code=500 timestamp="2025-06-16T12:53:20+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:20 | 500 |  120.532125ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=5"

2025/06/16 12:53:22 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[68.372ms] [34;1m[rows:0][0m SELECT * FROM `hook_apps` WHERE user_id = 8
time="2025-06-16T12:53:22+08:00" level=info msg="HTTP Request" body_size=42 client_ip="::1" error= latency=68.7815ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:53:22+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:22 | 200 |   69.058041ms |             ::1 | GET      "/api/v1/apps/"

2025/06/16 12:53:24 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[61.761ms] [34;1m[rows:0][0m SELECT * FROM `hook_apps` WHERE user_id = 8
time="2025-06-16T12:53:24+08:00" level=info msg="HTTP Request" body_size=42 client_ip="::1" error= latency=62.005875ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:53:24+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:24 | 200 |   62.166458ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:53:25 | 204 |      11.875µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:53:25 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[63.672ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 8 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:53:25+08:00" level=info msg="HTTP Request" body_size=218 client_ip="::1" error= latency=64.06325ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:53:25+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:25 | 200 |   64.194042ms |             ::1 | GET      "/api/v1/user/profile"

2025/06/16 12:53:26 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[62.565ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 8 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:53:26+08:00" level=info msg="HTTP Request" body_size=218 client_ip="::1" error= latency=63.023667ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:53:26+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:26 | 200 |    63.22275ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:53:26 | 204 |       9.459µs |             ::1 | OPTIONS  "/api/v1/apps/"

2025/06/16 12:53:26 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[64.042ms] [34;1m[rows:0][0m SELECT * FROM `hook_apps` WHERE user_id = 8
time="2025-06-16T12:53:26+08:00" level=info msg="HTTP Request" body_size=42 client_ip="::1" error= latency=64.368083ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:53:26+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:26 | 200 |   64.556375ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:53:26 | 204 |      12.334µs |             ::1 | OPTIONS  "/api/v1/user/balance-logs?page=1&page_size=5"

2025/06/16 12:53:26 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[60.880ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 8

2025/06/16 12:53:26 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[60.263ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 8 ORDER BY created_at DESC LIMIT 5
time="2025-06-16T12:53:26+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=121.655875ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=5" status_code=500 timestamp="2025-06-16T12:53:26+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:26 | 500 |   121.95475ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=5"

2025/06/16 12:53:27 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[60.313ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 8 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:53:27+08:00" level=info msg="HTTP Request" body_size=218 client_ip="::1" error= latency=60.751875ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:53:27+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:27 | 200 |   60.964041ms |             ::1 | GET      "/api/v1/user/profile"

2025/06/16 12:53:28 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[68.679ms] [34;1m[rows:0][0m SELECT * FROM `hook_apps` WHERE user_id = 8
time="2025-06-16T12:53:28+08:00" level=info msg="HTTP Request" body_size=42 client_ip="::1" error= latency=69.224834ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:53:28+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:28 | 200 |   69.440959ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:53:29 | 204 |      11.833µs |             ::1 | OPTIONS  "/api/v1/user/balance-logs?page=1&page_size=10"

2025/06/16 12:53:29 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[70.343ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 8

2025/06/16 12:53:29 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[59.981ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 8 ORDER BY created_at DESC LIMIT 10
time="2025-06-16T12:53:29+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=130.90825ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=10" status_code=500 timestamp="2025-06-16T12:53:29+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:53:29 | 500 |  131.137792ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=10"
[GIN] 2025/06/16 - 12:56:37 | 204 |      50.166µs |             ::1 | OPTIONS  "/api/v1/send-sms"
time="2025-06-16T12:56:37+08:00" level=info msg="HTTP Request" body_size=71 client_ip="::1" error= latency=340.625583ms method=POST path=/api/v1/send-sms status_code=200 timestamp="2025-06-16T12:56:37+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:56:37 | 200 |  340.807791ms |             ::1 | POST     "/api/v1/send-sms"
[GIN] 2025/06/16 - 12:56:44 | 204 |      10.542µs |             ::1 | OPTIONS  "/api/v1/register"

2025/06/16 12:56:44 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:25
[0m[33m[70.366ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_user` WHERE phone = '13200000000'

2025/06/16 12:56:44 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:45
[0m[33m[132.404ms] [34;1m[rows:1][0m INSERT INTO `hook_user` (`phone`,`password`,`role`,`nickname`,`balance`,`is_active`,`created_at`,`updated_at`) VALUES ('13200000000','$2a$10$RgwxAP8K.jwg6E4eMG64n.lEjfdzAl6js7HcPlxETOxvdUus1MV7y','user','qqq',0,true,'2025-06-16 12:56:44.793','2025-06-16 12:56:44.793')
time="2025-06-16T12:56:44+08:00" level=info msg="HTTP Request" body_size=126 client_ip="::1" error= latency=358.977375ms method=POST path=/api/v1/register status_code=200 timestamp="2025-06-16T12:56:44+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:56:44 | 200 |  359.082416ms |             ::1 | POST     "/api/v1/register"
[GIN] 2025/06/16 - 12:58:11 | 204 |      55.584µs |             ::1 | OPTIONS  "/api/v1/login"

2025/06/16 12:58:11 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:56
[0m[33m[63.159ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE phone = '13200000000' ORDER BY `hook_user`.`id` LIMIT 1

2025/06/16 12:58:11 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:90
[0m[33m[134.438ms] [34;1m[rows:1][0m UPDATE `hook_user` SET `updated_at`=NOW() WHERE `id` = 11
time="2025-06-16T12:58:11+08:00" level=info msg="HTTP Request" body_size=354 client_ip="::1" error= latency=273.617875ms method=POST path=/api/v1/login status_code=200 timestamp="2025-06-16T12:58:11+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:58:11 | 200 |   273.71675ms |             ::1 | POST     "/api/v1/login"
[GIN] 2025/06/16 - 12:58:11 | 204 |      11.875µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:58:11 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[59.837ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 11 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:58:11+08:00" level=info msg="HTTP Request" body_size=217 client_ip="::1" error= latency=60.160667ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:58:11+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:58:11 | 200 |      60.236ms |             ::1 | GET      "/api/v1/user/profile"
[GIN] 2025/06/16 - 12:58:11 | 204 |       9.625µs |             ::1 | OPTIONS  "/api/v1/apps/"

2025/06/16 12:58:11 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[56.802ms] [34;1m[rows:0][0m SELECT * FROM `hook_apps` WHERE user_id = 11
time="2025-06-16T12:58:11+08:00" level=info msg="HTTP Request" body_size=42 client_ip="::1" error= latency=57.151541ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:58:11+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:58:11 | 200 |   57.355542ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:58:11 | 204 |      10.291µs |             ::1 | OPTIONS  "/api/v1/user/balance-logs?page=1&page_size=5"

2025/06/16 12:58:11 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[59.959ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 11

2025/06/16 12:58:11 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[59.347ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 11 ORDER BY created_at DESC LIMIT 5
time="2025-06-16T12:58:11+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=119.920834ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=5" status_code=500 timestamp="2025-06-16T12:58:11+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:58:11 | 500 |  120.069041ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=5"

2025/06/16 12:58:13 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[62.798ms] [34;1m[rows:0][0m SELECT * FROM `hook_apps` WHERE user_id = 11
time="2025-06-16T12:58:13+08:00" level=info msg="HTTP Request" body_size=42 client_ip="::1" error= latency=62.96625ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:58:13+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:58:13 | 200 |   63.048375ms |             ::1 | GET      "/api/v1/apps/"

2025/06/16 12:58:14 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[56.815ms] [34;1m[rows:0][0m SELECT * FROM `hook_apps` WHERE user_id = 11
time="2025-06-16T12:58:14+08:00" level=info msg="HTTP Request" body_size=42 client_ip="::1" error= latency=57.090209ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:58:14+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:58:14 | 200 |   57.187958ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:58:18 | 204 |        8.75µs |             ::1 | OPTIONS  "/api/v1/apps/"

2025/06/16 12:58:18 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:175
[0m[33m[68.497ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_apps` WHERE app_id = 'aLYWDeqwlORShVHl'

2025/06/16 12:58:18 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:42
[0m[33m[129.880ms] [34;1m[rows:1][0m INSERT INTO `hook_apps` (`user_id`,`name`,`app_id`,`app_secret`,`status`,`total_calls`,`created_at`) VALUES (11,'qwdq','aLYWDeqwlORShVHl','WKDIhTvizFYVacdMbl2TpuEZkIYYPE4E',0,0,'2025-06-16 12:58:18.474')
time="2025-06-16T12:58:18+08:00" level=info msg="HTTP Request" body_size=387 client_ip="::1" error= latency=199.02325ms method=POST path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:58:18+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:58:18 | 200 |  199.197542ms |             ::1 | POST     "/api/v1/apps/"

2025/06/16 12:58:18 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[56.616ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE user_id = 11
time="2025-06-16T12:58:18+08:00" level=info msg="HTTP Request" body_size=378 client_ip="::1" error= latency=56.942625ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:58:18+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:58:18 | 200 |   57.076333ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:58:20 | 204 |      14.542µs |             ::1 | OPTIONS  "/api/v1/user/profile"

2025/06/16 12:58:20 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:66
[0m[33m[56.514ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 11 ORDER BY `hook_user`.`id` LIMIT 1
time="2025-06-16T12:58:20+08:00" level=info msg="HTTP Request" body_size=217 client_ip="::1" error= latency=56.883541ms method=GET path=/api/v1/user/profile status_code=200 timestamp="2025-06-16T12:58:20+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:58:20 | 200 |   57.001458ms |             ::1 | GET      "/api/v1/user/profile"

2025/06/16 12:58:20 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/app_service.go:63
[0m[33m[63.178ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE user_id = 11
time="2025-06-16T12:58:20+08:00" level=info msg="HTTP Request" body_size=378 client_ip="::1" error= latency=63.524959ms method=GET path=/api/v1/apps/ status_code=200 timestamp="2025-06-16T12:58:20+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:58:20 | 200 |   63.696458ms |             ::1 | GET      "/api/v1/apps/"
[GIN] 2025/06/16 - 12:58:20 | 204 |          11µs |             ::1 | OPTIONS  "/api/v1/user/balance-logs?page=1&page_size=5"

2025/06/16 12:58:20 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:165
[0m[33m[60.927ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_balance_logs` WHERE user_id = 11

2025/06/16 12:58:20 [31;1m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:172 [35;1mOperator: unsupported relations for schema BalanceLog
[0m[33m[59.757ms] [34;1m[rows:0][0m SELECT * FROM `hook_balance_logs` WHERE user_id = 11 ORDER BY created_at DESC LIMIT 5
time="2025-06-16T12:58:20+08:00" level=info msg="HTTP Request" body_size=98 client_ip="::1" error= latency=121.174792ms method=GET path="/api/v1/user/balance-logs?page=1&page_size=5" status_code=500 timestamp="2025-06-16T12:58:20+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:58:20 | 500 |  121.378458ms |             ::1 | GET      "/api/v1/user/balance-logs?page=1&page_size=5"
[GIN] 2025/06/16 - 12:58:53 | 204 |       36.25µs |             ::1 | OPTIONS  "/api/v1/send-sms"
time="2025-06-16T12:58:53+08:00" level=info msg="HTTP Request" body_size=71 client_ip="::1" error= latency=403.193708ms method=POST path=/api/v1/send-sms status_code=200 timestamp="2025-06-16T12:58:53+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:58:53 | 200 |    403.3275ms |             ::1 | POST     "/api/v1/send-sms"
[GIN] 2025/06/16 - 12:58:59 | 204 |        5.75µs |             ::1 | OPTIONS  "/api/v1/register"

2025/06/16 12:58:59 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:25
[0m[33m[60.013ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_user` WHERE phone = '13200000001'

2025/06/16 12:58:59 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:45
[0m[33m[122.638ms] [34;1m[rows:1][0m INSERT INTO `hook_user` (`phone`,`password`,`role`,`nickname`,`balance`,`is_active`,`created_at`,`updated_at`) VALUES ('13200000001','$2a$10$zVl4YcWq7METdFfFjglh9OSbCmdzOx8sB0.yiWzwr7ipo2yHN0Kq6','user','qqq1312',0,true,'2025-06-16 12:58:59.474','2025-06-16 12:58:59.474')
time="2025-06-16T12:58:59+08:00" level=info msg="HTTP Request" body_size=130 client_ip="::1" error= latency=335.018792ms method=POST path=/api/v1/register status_code=200 timestamp="2025-06-16T12:58:59+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 12:58:59 | 200 |  335.086459ms |             ::1 | POST     "/api/v1/register"
[GIN] 2025/06/16 - 13:02:50 | 204 |      52.708µs |             ::1 | OPTIONS  "/api/v1/send-sms"
time="2025-06-16T13:02:50+08:00" level=info msg="HTTP Request" body_size=71 client_ip="::1" error= latency=378.720208ms method=POST path=/api/v1/send-sms status_code=200 timestamp="2025-06-16T13:02:50+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 13:02:50 | 200 |  379.310041ms |             ::1 | POST     "/api/v1/send-sms"
[GIN] 2025/06/16 - 13:02:56 | 204 |        12.5µs |             ::1 | OPTIONS  "/api/v1/register"

2025/06/16 13:02:57 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:25
[0m[33m[60.083ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_user` WHERE phone = '13200000003'

2025/06/16 13:02:57 [32m/Users/<USER>/Documents/studio/GO/solve-go-api/internal/services/user_service.go:45
[0m[33m[132.772ms] [34;1m[rows:1][0m INSERT INTO `hook_user` (`phone`,`password`,`role`,`nickname`,`balance`,`is_active`,`created_at`,`updated_at`) VALUES ('13200000003','$2a$10$RxJuNB6eW5VoqD/W4tqd4OiJnJnrOUj0RnXZ393BdqAUC.ZF7cnjq','user','qqq1312123',0,true,'2025-06-16 13:02:57.246','2025-06-16 13:02:57.246')
time="2025-06-16T13:02:57+08:00" level=info msg="HTTP Request" body_size=133 client_ip="::1" error= latency=347.663167ms method=POST path=/api/v1/register status_code=200 timestamp="2025-06-16T13:02:57+08:00" user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.5 Safari/605.1.15"
[GIN] 2025/06/16 - 13:02:57 | 200 |  347.746916ms |             ::1 | POST     "/api/v1/register"
