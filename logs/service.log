2025/06/17 20:01:21 Warning: .env file not found: unexpected character "-" in variable name near "curl -X GET \"http://localhost:8080/api/v1/manager/logs/?page=1&page_size=20\" \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 获取单个日志\ncurl -X GET http://localhost:8080/api/v1/manager/logs/{log_id} \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 删除日志\ncurl -X DELETE http://localhost:8080/api/v1/manager/logs/{log_id} \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 获取日志统计\ncurl -X GET http://localhost:8080/api/v1/manager/logs/stats \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 8. 超级管理员接口 (需要admin权限)\n\n# 获取所有用户列表 (支持过滤)\ncurl -X GET \"http://localhost:8080/api/v1/admin/users/?page=1&page_size=20&role=user&is_active=1\" \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 获取单个用户详情\ncurl -X GET http://localhost:8080/api/v1/admin/users/{user_id} \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 更新用户信息\ncurl -X PUT http://localhost:8080/api/v1/admin/users/{user_id} \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\" \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\n    \"nickname\": \"新昵称\",\n    \"is_active\": 1,\n    \"balance\": 5000\n  }'\n\n# 获取用户统计信息\ncurl -X GET http://localhost:8080/api/v1/admin/users/stats \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 应用管理 (开发中)\ncurl -X GET http://localhost:8080/api/v1/admin/apps/ \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 系统配置管理 (开发中)\ncurl -X GET http://localhost:8080/api/v1/admin/config/ \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\"\n\n# 模型配置管理 (开发中)\ncurl -X GET http://localhost:8080/api/v1/admin/models/ \\\n  -H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoiYWRtaW4iLCJpc3MiOiJzb2x2ZS1nby1hcGkiLCJleHAiOjE3NTAyMzQ3MDIsIm5iZiI6MTc1MDE0ODMwMiwiaWF0IjoxNzUwMTQ4MzAyfQ.h9MDbNktI4QLbyoXJ7WVaBzTAGwlSiCP3NRDBDfMnoE\""

2025/06/17 20:01:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[31.914ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[66.213ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[68.200ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND table_type = 'BASE TABLE'

2025/06/17 20:01:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[32.455ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[138.402ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[65.480ms] [34;1m[rows:-][0m SELECT * FROM `hook_user` LIMIT 1

2025/06/17 20:01:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[67.842ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_user' ORDER BY ORDINAL_POSITION

2025/06/17 20:01:22 [32m
[0m[33m[32.393ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:22 [32m
[0m[33m[65.532ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:22 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[68.767ms] [34;1m[rows:2][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_user' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/17 20:01:22 [32m
[0m[33m[32.497ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:22 [32m
[0m[33m[65.102ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:22 [32m
[0m[33m[130.254ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_user' AND constraint_name = 'uni_hook_user_phone'

2025/06/17 20:01:23 [32m
[0m[33m[32.246ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:23 [32m
[0m[33m[64.766ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:23 [32m
[0m[33m[68.732ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND index_name = 'idx_hook_user_phone'

2025/06/17 20:01:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[32.507ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[68.490ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[67.103ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_user' AND index_name = 'idx_hook_user_phone'

2025/06/17 20:01:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.958ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[62.944ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[127.267ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND table_type = 'BASE TABLE'

2025/06/17 20:01:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[31.741ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[62.577ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[63.280ms] [34;1m[rows:-][0m SELECT * FROM `hook_apps` LIMIT 1

2025/06/17 20:01:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[62.903ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' ORDER BY ORDINAL_POSITION

2025/06/17 20:01:23 [32m
[0m[33m[31.763ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:23 [32m
[0m[33m[63.313ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:23 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[65.929ms] [34;1m[rows:3][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_apps' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/17 20:01:24 [32m
[0m[33m[111.048ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:24 [32m
[0m[33m[67.510ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:24 [32m
[0m[33m[67.985ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_apps' AND constraint_name = 'uni_hook_apps_app_id'

2025/06/17 20:01:24 [32m
[0m[33m[31.999ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:24 [32m
[0m[33m[63.263ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:24 [32m
[0m[33m[63.433ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_app_id'

2025/06/17 20:01:24 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[31.164ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:24 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[62.392ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:24 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[134.387ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_user_id'

2025/06/17 20:01:24 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[32.542ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:24 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[64.144ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:24 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[63.050ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_apps' AND index_name = 'idx_hook_apps_app_id'

2025/06/17 20:01:24 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[31.380ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:24 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[63.540ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:24 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[63.331ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_balance_logs' AND table_type = 'BASE TABLE'

2025/06/17 20:01:24 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[31.530ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:24 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[63.342ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:25 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[141.501ms] [34;1m[rows:-][0m SELECT * FROM `hook_balance_logs` LIMIT 1

2025/06/17 20:01:25 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[63.503ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_balance_logs' ORDER BY ORDINAL_POSITION

2025/06/17 20:01:25 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[32.310ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:25 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[64.694ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:25 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[63.522ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_balance_logs' AND index_name = 'idx_hook_balance_logs_user_id'

2025/06/17 20:01:25 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[32.069ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:25 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[61.853ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:25 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[63.297ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' AND table_type = 'BASE TABLE'

2025/06/17 20:01:25 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[112.085ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:25 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[64.018ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:25 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[62.362ms] [34;1m[rows:-][0m SELECT * FROM `hook_system_config` LIMIT 1

2025/06/17 20:01:25 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[64.378ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' ORDER BY ORDINAL_POSITION

2025/06/17 20:01:25 [32m
[0m[33m[31.209ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:25 [32m
[0m[33m[64.009ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:25 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[64.954ms] [34;1m[rows:2][0m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_system_config' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX

2025/06/17 20:01:26 [32m
[0m[33m[31.906ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:26 [32m
[0m[33m[137.872ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:26 [32m
[0m[33m[63.258ms] [34;1m[rows:-][0m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_system_config' AND constraint_name = 'uni_hook_system_config_key'

2025/06/17 20:01:26 [32m
[0m[33m[31.530ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:26 [32m
[0m[33m[63.810ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:26 [32m
[0m[33m[63.272ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' AND index_name = 'idx_hook_system_config_key'

2025/06/17 20:01:26 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[31.270ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:26 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[63.534ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:26 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[61.769ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_system_config' AND index_name = 'idx_hook_system_config_key'

2025/06/17 20:01:26 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[31.929ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:26 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[142.858ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:26 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[62.964ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_models' AND table_type = 'BASE TABLE'

2025/06/17 20:01:26 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[31.669ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:26 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[64.194ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:26 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[63.431ms] [34;1m[rows:-][0m SELECT * FROM `hook_models` LIMIT 1

2025/06/17 20:01:26 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[64.249ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_models' ORDER BY ORDINAL_POSITION

2025/06/17 20:01:27 [32m
[0m[33m[31.089ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:27 [32m
[0m[33m[64.276ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:27 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47 [33mSLOW SQL >= 200ms
[0m[31;1m[332.342ms] [33m[rows:2][35m 
SELECT
	TABLE_NAME,
	COLUMN_NAME,
	INDEX_NAME,
	NON_UNIQUE 
FROM
	information_schema.STATISTICS 
WHERE
	TABLE_SCHEMA = 'solve_web' 
	AND TABLE_NAME = 'hook_models' 
ORDER BY
	INDEX_NAME,
	SEQ_IN_INDEX[0m

2025/06/17 20:01:27 [32m
[0m[33m[31.044ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:27 [32m
[0m[33m[62.844ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:27 [32m [33mSLOW SQL >= 200ms
[0m[31;1m[399.646ms] [33m[rows:-][35m SELECT count(*) FROM INFORMATION_SCHEMA.table_constraints WHERE constraint_schema = 'solve_web' AND table_name = 'hook_models' AND constraint_name = 'uni_hook_models_model_name'[0m

2025/06/17 20:01:27 [32m
[0m[33m[31.382ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:28 [32m
[0m[33m[63.395ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:28 [32m [33mSLOW SQL >= 200ms
[0m[31;1m[459.623ms] [33m[rows:-][35m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_models' AND index_name = 'idx_hook_models_model_name'[0m

2025/06/17 20:01:28 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[43.411ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `temperature` decimal(3,2) DEFAULT 0.7 COMMENT '温度参数'

2025/06/17 20:01:28 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[43.116ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `top_p` decimal(3,2) DEFAULT 0.9 COMMENT 'TopP参数'

2025/06/17 20:01:28 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[40.529ms] [34;1m[rows:0][0m ALTER TABLE `hook_models` MODIFY COLUMN `repetition_penalty` decimal(3,2) DEFAULT 1 COMMENT '重复惩罚'

2025/06/17 20:01:28 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47 [33mSLOW SQL >= 200ms
[0m[31;1m[363.238ms] [33m[rows:0][35m ALTER TABLE `hook_models` MODIFY COLUMN `presence_penalty` decimal(3,2) DEFAULT 0 COMMENT '存在惩罚'[0m

2025/06/17 20:01:28 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[31.440ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:29 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[63.423ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:29 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47 [33mSLOW SQL >= 200ms
[0m[31;1m[269.473ms] [33m[rows:-][35m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_models' AND index_name = 'idx_hook_models_model_name'[0m

2025/06/17 20:01:29 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[31.608ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:29 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[64.677ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:29 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[84.571ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_question_bank' AND table_type = 'BASE TABLE'

2025/06/17 20:01:29 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[31.551ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:29 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[90.026ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:29 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[188.427ms] [34;1m[rows:-][0m SELECT * FROM `hook_question_bank` LIMIT 1

2025/06/17 20:01:29 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[65.691ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_question_bank' ORDER BY ORDINAL_POSITION

2025/06/17 20:01:29 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[32.361ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:29 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[84.750ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:30 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[63.070ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_question_bank' AND index_name = 'idx_hook_question_bank_hash_key'

2025/06/17 20:01:30 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.309ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:30 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[62.850ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:30 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[62.585ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.tables WHERE table_schema = 'solve_web' AND table_name = 'hook_solve_logs' AND table_type = 'BASE TABLE'

2025/06/17 20:01:30 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[30.631ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:30 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[62.163ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:30 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[61.899ms] [34;1m[rows:-][0m SELECT * FROM `hook_solve_logs` LIMIT 1

2025/06/17 20:01:30 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[61.930ms] [34;1m[rows:-][0m SELECT column_name, column_default, is_nullable = 'YES', data_type, character_maximum_length, column_type, column_key, extra, column_comment, numeric_precision, numeric_scale , datetime_precision FROM information_schema.columns WHERE table_schema = 'solve_web' AND table_name = 'hook_solve_logs' ORDER BY ORDINAL_POSITION

2025/06/17 20:01:30 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[31.467ms] [34;1m[rows:-][0m SELECT DATABASE()

2025/06/17 20:01:30 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[64.011ms] [34;1m[rows:1][0m SELECT SCHEMA_NAME from Information_schema.SCHEMATA where SCHEMA_NAME LIKE 'solve_web%' ORDER BY SCHEMA_NAME='solve_web' DESC,SCHEMA_NAME limit 1

2025/06/17 20:01:30 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:47
[0m[33m[62.246ms] [34;1m[rows:-][0m SELECT count(*) FROM information_schema.statistics WHERE table_schema = 'solve_web' AND table_name = 'hook_solve_logs' AND index_name = 'idx_hook_solve_logs_app_id'

2025/06/17 20:01:30 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/database/mysql.go:67
[0m[33m[62.830ms] [34;1m[rows:1][0m SELECT count(*) FROM `hook_user` WHERE role IN ('admin','manager')
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> solve-go-api/internal/router.Setup.func1 (6 handlers)
[GIN-debug] POST   /api/v1/send-sms          --> solve-go-api/internal/handlers.(*AuthHandler).SendSMSCode-fm (6 handlers)
[GIN-debug] POST   /api/v1/register          --> solve-go-api/internal/handlers.(*AuthHandler).Register-fm (6 handlers)
[GIN-debug] POST   /api/v1/login             --> solve-go-api/internal/handlers.(*AuthHandler).Login-fm (6 handlers)
[GIN-debug] POST   /api/v1/forgot-password   --> solve-go-api/internal/handlers.(*AuthHandler).ForgotPassword-fm (6 handlers)
[GIN-debug] POST   /api/v1/reset-password    --> solve-go-api/internal/handlers.(*AuthHandler).ResetPassword-fm (6 handlers)
[GIN-debug] POST   /api/v1/solve/question    --> solve-go-api/internal/handlers.(*SolveHandler).SolveQuestion-fm (7 handlers)
[GIN-debug] GET    /api/v1/user/profile      --> solve-go-api/internal/handlers.(*AuthHandler).GetProfile-fm (7 handlers)
[GIN-debug] PUT    /api/v1/user/profile      --> solve-go-api/internal/handlers.(*AuthHandler).UpdateProfile-fm (7 handlers)
[GIN-debug] POST   /api/v1/user/change-password --> solve-go-api/internal/handlers.(*AuthHandler).ChangePassword-fm (7 handlers)
[GIN-debug] GET    /api/v1/user/balance-logs --> solve-go-api/internal/handlers.(*AuthHandler).GetBalanceLogs-fm (7 handlers)
[GIN-debug] GET    /api/v1/apps/             --> solve-go-api/internal/handlers.(*AppHandler).GetApps-fm (7 handlers)
[GIN-debug] POST   /api/v1/apps/             --> solve-go-api/internal/handlers.(*AppHandler).CreateApp-fm (7 handlers)
[GIN-debug] PUT    /api/v1/apps/:id          --> solve-go-api/internal/handlers.(*AppHandler).UpdateApp-fm (7 handlers)
[GIN-debug] DELETE /api/v1/apps/:id          --> solve-go-api/internal/handlers.(*AppHandler).DeleteApp-fm (7 handlers)
[GIN-debug] POST   /api/v1/apps/:id/reset-secret --> solve-go-api/internal/handlers.(*AppHandler).ResetSecret-fm (7 handlers)
[GIN-debug] GET    /api/v1/apps/:id/logs     --> solve-go-api/internal/handlers.(*AppHandler).GetAppLogs-fm (7 handlers)
[GIN-debug] GET    /api/v1/manager/questions/ --> solve-go-api/internal/handlers.(*QuestionHandler).GetQuestions-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/questions/:id --> solve-go-api/internal/handlers.(*QuestionHandler).GetQuestion-fm (8 handlers)
[GIN-debug] PUT    /api/v1/manager/questions/:id --> solve-go-api/internal/handlers.(*QuestionHandler).UpdateQuestion-fm (8 handlers)
[GIN-debug] DELETE /api/v1/manager/questions/:id --> solve-go-api/internal/handlers.(*QuestionHandler).DeleteQuestion-fm (8 handlers)
[GIN-debug] POST   /api/v1/manager/questions/:id/verify --> solve-go-api/internal/handlers.(*QuestionHandler).VerifyQuestion-fm (8 handlers)
[GIN-debug] POST   /api/v1/manager/questions/:id/unverify --> solve-go-api/internal/handlers.(*QuestionHandler).UnverifyQuestion-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/questions/stats --> solve-go-api/internal/handlers.(*QuestionHandler).GetStats-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/logs/     --> solve-go-api/internal/handlers.(*LogHandler).GetLogs-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/logs/:id  --> solve-go-api/internal/handlers.(*LogHandler).GetLog-fm (8 handlers)
[GIN-debug] DELETE /api/v1/manager/logs/:id  --> solve-go-api/internal/handlers.(*LogHandler).DeleteLog-fm (8 handlers)
[GIN-debug] GET    /api/v1/manager/logs/stats --> solve-go-api/internal/handlers.(*LogHandler).GetStats-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/      --> solve-go-api/internal/handlers.(*AdminHandler).GetUsers-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/:id   --> solve-go-api/internal/handlers.(*AdminHandler).GetUser-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/users/:id   --> solve-go-api/internal/handlers.(*AdminHandler).UpdateUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/activate --> solve-go-api/internal/handlers.(*AdminHandler).ActivateUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/deactivate --> solve-go-api/internal/handlers.(*AdminHandler).DeactivateUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/recharge --> solve-go-api/internal/handlers.(*AdminHandler).RechargeUser-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/stats --> solve-go-api/internal/handlers.(*AdminHandler).GetUserStats-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/users/pending --> solve-go-api/internal/handlers.(*AdminHandler).GetPendingUsers-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/approve --> solve-go-api/internal/handlers.(*AdminHandler).ApproveUser-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/users/:id/reject --> solve-go-api/internal/handlers.(*AdminHandler).RejectUser-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/apps/       --> solve-go-api/internal/handlers.(*AdminHandler).GetAllApps-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/apps/:id    --> solve-go-api/internal/handlers.(*AdminHandler).UpdateApp-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/apps/:id/activate --> solve-go-api/internal/handlers.(*AdminHandler).ActivateApp-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/apps/:id/deactivate --> solve-go-api/internal/handlers.(*AdminHandler).DeactivateApp-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/config/     --> solve-go-api/internal/handlers.(*AdminHandler).GetConfigs-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/config/:key --> solve-go-api/internal/handlers.(*AdminHandler).UpdateConfig-fm (8 handlers)
[GIN-debug] GET    /api/v1/admin/models/     --> solve-go-api/internal/handlers.(*AdminHandler).GetModels-fm (8 handlers)
[GIN-debug] POST   /api/v1/admin/models/     --> solve-go-api/internal/handlers.(*AdminHandler).CreateModel-fm (8 handlers)
[GIN-debug] PUT    /api/v1/admin/models/:id  --> solve-go-api/internal/handlers.(*AdminHandler).UpdateModel-fm (8 handlers)
[GIN-debug] DELETE /api/v1/admin/models/:id  --> solve-go-api/internal/handlers.(*AdminHandler).DeleteModel-fm (8 handlers)
2025/06/17 20:01:30 Server starting on port 8080
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
[GIN-debug] Listening and serving HTTP on :8080

2025/06/17 20:01:45 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:95
[0m[33m[63.828ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' AND app_secret = 'VsubbUlgZejHgRrylJ0qs3A9HJy3a8P2' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/17 20:01:45 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:111
[0m[33m[62.646ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/17 20:01:45 🎯 [HANDLER] 收到解题请求 - IP: ::1, UserAgent: PostmanRuntime-ApipostRuntime/1.1.0
2025/06/17 20:01:45 ✅ [HANDLER] 请求参数解析成功 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 20:01:45 🚀 [HANDLER] 开始处理解题请求
2025/06/17 20:01:45 🚀 [SOLVE] 开始处理解题请求 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 20:01:45 📋 [SOLVE] 步骤1: 验证应用和用户

2025/06/17 20:01:45 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:53
[0m[33m[63.155ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/17 20:01:45 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:111
[0m[33m[63.387ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/17 20:01:45 ✅ [SOLVE] 应用/用户验证成功 - UserID: 20, Balance: 500000
2025/06/17 20:01:45 💰 [SOLVE] 步骤2: 检查用户积分 - 当前积分: 500000
2025/06/17 20:01:45 🔍 [SOLVE] 步骤3: 调用OCR模型识别图片
2025/06/17 20:01:45 🔍 [OCR] 开始OCR处理 - 模型: qwen-vl-plus, 图片URL: http://img.igmdns.com/images/cc0001.jpg

2025/06/17 20:01:45 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/model_service.go:31
[0m[33m[64.477ms] [34;1m[rows:1][0m SELECT * FROM `hook_models` WHERE model_name = 'qwen-vl-plus' ORDER BY `hook_models`.`id` LIMIT 1
2025/06/17 20:01:47 🔍 [HTTP-DEBUG] 响应状态码: 200
2025/06/17 20:01:47 🔍 [HTTP-DEBUG] 响应体长度: 755
2025/06/17 20:01:47 🔍 [HTTP-DEBUG] 响应体前500字符: {"output":{"choices":[{"finish_reason":"stop","message":{"role":"assistant","content":[{"text":"```json\n{\n    \"qutext\": \"(单选题)14、如图所示，驾车遇到此情况时应当注意什么？\",\n    \"options\": {\n        \"A\": \"左侧A柱盲区内可能有行人将要通过\",\n        \"B\": \"对向车道车辆将要调头\",\n        \"C\": \"后面有车辆将超车\",\n        \"D\": \"右侧车道有车辆将要通过\"\n    }\n}\n```"}]}}]},"usage":{"input_tokens_details":
2025/06/17 20:01:47 ✅ [OCR] 模型调用成功 - Token消耗: 1030
2025/06/17 20:01:47 📝 [OCR] 开始解析OCR响应
2025/06/17 20:01:47 🔍 [OCR-DEBUG] Content类型: []interface {}
2025/06/17 20:01:47 🔍 [OCR-DEBUG] Array类型，元素数量: 1
2025/06/17 20:01:47 🔍 [OCR-DEBUG] Array[0]类型: map[string]interface {}
2025/06/17 20:01:47 🔍 [OCR-DEBUG] 提取文本: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 20:01:47 🔍 [OCR-DEBUG] 最终内容长度: 332
2025/06/17 20:01:47 🔍 [OCR-DEBUG] 最终内容前200字符: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 20:01:47 🔍 [OCR-DEBUG] 最终内容后200字符: : "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 20:01:47 🔍 [JSON-EXTRACT] 开始提取JSON，原始内容: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 20:01:47 🔍 [JSON-EXTRACT] 从```json```中提取: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 20:01:47 🔍 [FIELD-MAP] 开始字段映射，原始JSON: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 20:01:47 🔍 [FIELD-MAP] 映射完成，结果: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 20:01:47 🔍 [OCR-DEBUG] 字段映射后的JSON: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 20:01:47 🔍 [TYPE-DETECT] 开始检测题目类型，题干内容: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 20:01:47 🔍 [TYPE-DETECT] 原始内容: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 20:01:47 🔍 [TYPE-DETECT] 转换小写后: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 20:01:47 🔍 [TYPE-DETECT] 检测到单选题
2025/06/17 20:01:47 🔍 [TYPE-DETECT] 检测结果: 单选题
2025/06/17 20:01:47 ✅ [OCR] 响应解析成功 - 题目类型: 单选题, 内容长度: 76
2025/06/17 20:01:47 ✅ [SOLVE] OCR处理成功 - Token消耗: 1030, 题目类型: 单选题
2025/06/17 20:01:47 🔄 [SOLVE] 步骤4: 检查Redis缓存
2025/06/17 20:01:48 ⚪ [SOLVE] Redis缓存未命中
2025/06/17 20:01:48 🎯 [SOLVE] 步骤5: 检查MySQL精确匹配

2025/06/17 20:01:48 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/question_service.go:23
[0m[33m[63.933ms] [34;1m[rows:0][0m SELECT * FROM `hook_question_bank` WHERE hash_key = '207a8f172a7f86f389c93d012941cdb0'
2025/06/17 20:01:48 ⚪ [SOLVE] MySQL精确匹配未命中
2025/06/17 20:01:48 🔍 [SOLVE] 步骤6: 检查MySQL模糊匹配

2025/06/17 20:01:48 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/match_service.go:29
[0m[33m[64.121ms] [34;1m[rows:0][0m SELECT * FROM `hook_question_bank` WHERE verified = true AND type = 2
2025/06/17 20:01:48 ⚪ [SOLVE] MySQL模糊匹配未命中
2025/06/17 20:01:48 🤖 [SOLVE] 步骤7: 调用Solve模型解答

2025/06/17 20:01:48 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/model_service.go:31
[0m[33m[63.630ms] [34;1m[rows:1][0m SELECT * FROM `hook_models` WHERE model_name = 'qwen-plus' ORDER BY `hook_models`.`id` LIMIT 1
2025/06/17 20:01:55 🔍 [HTTP-DEBUG] 响应状态码: 200
2025/06/17 20:01:55 🔍 [HTTP-DEBUG] 响应体长度: 851
2025/06/17 20:01:55 🔍 [HTTP-DEBUG] 响应体前500字符: {"output":{"finish_reason":"stop","text":"```json\n{\n  \"answer\": \"A\",\n  \"analysis\": \"解析内容：根据题干描述，驾驶员在行车过程中需要特别注意视线盲区的情况。选项A提到左侧A柱盲区内可能有行人将要通过，这是正确的，因为A柱确实会遮挡驾驶员的部分视线，形成盲区，尤其是在转弯或行人较多的情况下，需要格外留意。而其他选项中，B（对向车道车辆将要调头）、C（后面有车辆将�
2025/06/17 20:01:55 🔍 [HTTP-DEBUG] Choices为空，尝试处理Solve模型格式
2025/06/17 20:01:55 🔍 [HTTP-DEBUG] 从output.text提取内容: ```json
{
  "answer": "A",
  "analysis": "解析内容：根据题干描述，驾驶员在行车过�
2025/06/17 20:01:55 🔍 [HTTP-DEBUG] 成功转换为标准格式
2025/06/17 20:01:55 🔍 [SOLVE-DEBUG] 原始响应Choices数量: 1
2025/06/17 20:01:55 🔍 [SOLVE-DEBUG] 原始响应Usage: {TotalTokens:274}
2025/06/17 20:01:55 🔍 [SOLVE-DEBUG] 第一个Choice内容类型: string
2025/06/17 20:01:55 🔍 [SOLVE-DEBUG] 第一个Choice内容: ```json
{
  "answer": "A",
  "analysis": "解析内容：根据题干描述，驾驶员在行车过程中需要特别注意视线盲区的情况。选项A提到左侧A柱盲区内可能有行人将要通过，这是正确的，因为A柱确实会遮挡驾驶员的部分视线，形成盲区，尤其是在转弯或行人较多的情况下，需要格外留意。而其他选项中，B（对向车道车辆将要调头）、C（后面有车辆将超车）和D（右侧车道有车辆将要通过）虽然在某些情况下也需要关注，但与题目中提到的‘注意盲区’关系不大，因此正确答案为A。"
}
```
2025/06/17 20:01:55 ❌ [SOLVE] Solve模型处理失败: failed to parse solve response JSON: invalid character '`' looking for beginning of value
2025/06/17 20:01:55 📝 [HANDLER] 记录解题日志

2025/06/17 20:01:55 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/log_service.go:41
[0m[33m[129.115ms] [34;1m[rows:1][0m INSERT INTO `hook_solve_logs` (`app_id`,`user_url`,`matched_id`,`ocr_token`,`source`,`data`,`status`,`latency`,`created_at`) VALUES ('NdOCNqqWPtrLFvZj','http://img.igmdns.com/images/cc0001.jpg',0,1030,'',(NULL),0,10091,'2025-06-17 20:01:55.821')
2025/06/17 20:01:55 ❌ [HANDLER] 解题处理失败 - Status: 0, Message: failed to parse solve response JSON: invalid character '`' looking for beginning of value
time="2025-06-17T20:01:55+08:00" level=info msg="HTTP Request" body_size=114 client_ip="::1" error= latency=10.347496417s method=POST path=/api/v1/solve/question status_code=200 timestamp="2025-06-17T20:01:55+08:00" user_agent=PostmanRuntime-ApipostRuntime/1.1.0
[GIN] 2025/06/17 - 20:01:55 | 200 | 10.347635875s |             ::1 | POST     "/api/v1/solve/question"

2025/06/17 20:01:58 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:95
[0m[33m[67.718ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' AND app_secret = 'VsubbUlgZejHgRrylJ0qs3A9HJy3a8P2' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/17 20:01:58 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:111
[0m[33m[64.383ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/17 20:01:58 🎯 [HANDLER] 收到解题请求 - IP: ::1, UserAgent: PostmanRuntime-ApipostRuntime/1.1.0
2025/06/17 20:01:58 ✅ [HANDLER] 请求参数解析成功 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 20:01:58 🚀 [HANDLER] 开始处理解题请求
2025/06/17 20:01:58 🚀 [SOLVE] 开始处理解题请求 - AppID: NdOCNqqWPtrLFvZj, ImageURL: http://img.igmdns.com/images/cc0001.jpg
2025/06/17 20:01:58 📋 [SOLVE] 步骤1: 验证应用和用户

2025/06/17 20:01:59 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:53
[0m[33m[62.818ms] [34;1m[rows:1][0m SELECT * FROM `hook_apps` WHERE app_id = 'NdOCNqqWPtrLFvZj' ORDER BY `hook_apps`.`id` LIMIT 1

2025/06/17 20:01:59 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/app_service.go:111
[0m[33m[63.102ms] [34;1m[rows:1][0m SELECT * FROM `hook_user` WHERE `hook_user`.`id` = 20 ORDER BY `hook_user`.`id` LIMIT 1
2025/06/17 20:01:59 ✅ [SOLVE] 应用/用户验证成功 - UserID: 20, Balance: 500000
2025/06/17 20:01:59 💰 [SOLVE] 步骤2: 检查用户积分 - 当前积分: 500000
2025/06/17 20:01:59 🔍 [SOLVE] 步骤3: 调用OCR模型识别图片
2025/06/17 20:01:59 🔍 [OCR] 开始OCR处理 - 模型: qwen-vl-plus, 图片URL: http://img.igmdns.com/images/cc0001.jpg

2025/06/17 20:01:59 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/model_service.go:31
[0m[33m[63.248ms] [34;1m[rows:1][0m SELECT * FROM `hook_models` WHERE model_name = 'qwen-vl-plus' ORDER BY `hook_models`.`id` LIMIT 1
2025/06/17 20:02:01 🔍 [HTTP-DEBUG] 响应状态码: 200
2025/06/17 20:02:01 🔍 [HTTP-DEBUG] 响应体长度: 755
2025/06/17 20:02:01 🔍 [HTTP-DEBUG] 响应体前500字符: {"output":{"choices":[{"finish_reason":"stop","message":{"role":"assistant","content":[{"text":"```json\n{\n    \"qutext\": \"(单选题)14、如图所示，驾车遇到此情况时应当注意什么？\",\n    \"options\": {\n        \"A\": \"左侧A柱盲区内可能有行人将要通过\",\n        \"B\": \"对向车道车辆将要调头\",\n        \"C\": \"后面有车辆将超车\",\n        \"D\": \"右侧车道有车辆将要通过\"\n    }\n}\n```"}]}}]},"usage":{"input_tokens_details":
2025/06/17 20:02:01 ✅ [OCR] 模型调用成功 - Token消耗: 1030
2025/06/17 20:02:01 📝 [OCR] 开始解析OCR响应
2025/06/17 20:02:01 🔍 [OCR-DEBUG] Content类型: []interface {}
2025/06/17 20:02:01 🔍 [OCR-DEBUG] Array类型，元素数量: 1
2025/06/17 20:02:01 🔍 [OCR-DEBUG] Array[0]类型: map[string]interface {}
2025/06/17 20:02:01 🔍 [OCR-DEBUG] 提取文本: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 20:02:01 🔍 [OCR-DEBUG] 最终内容长度: 332
2025/06/17 20:02:01 🔍 [OCR-DEBUG] 最终内容前200字符: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 20:02:01 🔍 [OCR-DEBUG] 最终内容后200字符: : "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 20:02:01 🔍 [JSON-EXTRACT] 开始提取JSON，原始内容: ```json
{
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
```
2025/06/17 20:02:01 🔍 [JSON-EXTRACT] 从```json```中提取: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 20:02:01 🔍 [FIELD-MAP] 开始字段映射，原始JSON: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 20:02:01 🔍 [FIELD-MAP] 映射完成，结果: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 20:02:01 🔍 [OCR-DEBUG] 字段映射后的JSON: {
    "qutext": "(单选题)14、如图所示，驾车遇到此情况时应当注意什么？",
    "options": {
        "A": "左侧A柱盲区内可能有行人将要通过",
        "B": "对向车道车辆将要调头",
        "C": "后面有车辆将超车",
        "D": "右侧车道有车辆将要通过"
    }
}
2025/06/17 20:02:01 🔍 [TYPE-DETECT] 开始检测题目类型，题干内容: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 20:02:01 🔍 [TYPE-DETECT] 原始内容: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 20:02:01 🔍 [TYPE-DETECT] 转换小写后: (单选题)14、如图所示，驾车遇到此情况时应当注意什么？
2025/06/17 20:02:01 🔍 [TYPE-DETECT] 检测到单选题
2025/06/17 20:02:01 🔍 [TYPE-DETECT] 检测结果: 单选题
2025/06/17 20:02:01 ✅ [OCR] 响应解析成功 - 题目类型: 单选题, 内容长度: 76
2025/06/17 20:02:01 ✅ [SOLVE] OCR处理成功 - Token消耗: 1030, 题目类型: 单选题
2025/06/17 20:02:01 🔄 [SOLVE] 步骤4: 检查Redis缓存
2025/06/17 20:02:01 ⚪ [SOLVE] Redis缓存未命中
2025/06/17 20:02:01 🎯 [SOLVE] 步骤5: 检查MySQL精确匹配

2025/06/17 20:02:01 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/question_service.go:23
[0m[33m[63.177ms] [34;1m[rows:0][0m SELECT * FROM `hook_question_bank` WHERE hash_key = '207a8f172a7f86f389c93d012941cdb0'
2025/06/17 20:02:01 ⚪ [SOLVE] MySQL精确匹配未命中
2025/06/17 20:02:01 🔍 [SOLVE] 步骤6: 检查MySQL模糊匹配

2025/06/17 20:02:01 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/match_service.go:29
[0m[33m[63.320ms] [34;1m[rows:0][0m SELECT * FROM `hook_question_bank` WHERE verified = true AND type = 2
2025/06/17 20:02:01 ⚪ [SOLVE] MySQL模糊匹配未命中
2025/06/17 20:02:01 🤖 [SOLVE] 步骤7: 调用Solve模型解答

2025/06/17 20:02:01 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/model_service.go:31
[0m[33m[64.646ms] [34;1m[rows:1][0m SELECT * FROM `hook_models` WHERE model_name = 'qwen-plus' ORDER BY `hook_models`.`id` LIMIT 1
2025/06/17 20:02:17 🔍 [HTTP-DEBUG] 响应状态码: 200
2025/06/17 20:02:17 🔍 [HTTP-DEBUG] 响应体长度: 909
2025/06/17 20:02:17 🔍 [HTTP-DEBUG] 响应体前500字符: {"output":{"finish_reason":"stop","text":"```json\n{\n  \"answer\": \"A\",\n  \"analysis\": \"解析内容：在驾驶过程中，A柱盲区是一个非常重要的安全隐患区域。根据题干中的描述和选项分析，选项A提到左侧A柱盲区内可能有行人将要通过，这是驾驶员需要特别注意的情况。A柱盲区会遮挡驾驶员的视线，尤其是在转弯或观察行人时，容易发生危险。而其他选项中，B（对向车道车辆将要调头）、C（�
2025/06/17 20:02:17 🔍 [HTTP-DEBUG] Choices为空，尝试处理Solve模型格式
2025/06/17 20:02:17 🔍 [HTTP-DEBUG] 从output.text提取内容: ```json
{
  "answer": "A",
  "analysis": "解析内容：在驾驶过程中，A柱盲区是一个非
2025/06/17 20:02:17 🔍 [HTTP-DEBUG] 成功转换为标准格式
2025/06/17 20:02:17 🔍 [SOLVE-DEBUG] 原始响应Choices数量: 1
2025/06/17 20:02:17 🔍 [SOLVE-DEBUG] 原始响应Usage: {TotalTokens:288}
2025/06/17 20:02:17 🔍 [SOLVE-DEBUG] 第一个Choice内容类型: string
2025/06/17 20:02:17 🔍 [SOLVE-DEBUG] 第一个Choice内容: ```json
{
  "answer": "A",
  "analysis": "解析内容：在驾驶过程中，A柱盲区是一个非常重要的安全隐患区域。根据题干中的描述和选项分析，选项A提到左侧A柱盲区内可能有行人将要通过，这是驾驶员需要特别注意的情况。A柱盲区会遮挡驾驶员的视线，尤其是在转弯或观察行人时，容易发生危险。而其他选项中，B（对向车道车辆将要调头）、C（后面有车辆将超车）、D（右侧车道有车辆将要通过）虽然也可能是驾驶中需要注意的情况，但与题目中提到的‘如图所示’和‘此情况’的描述不完全匹配，因此正确答案为A。"
}
```
2025/06/17 20:02:17 ❌ [SOLVE] Solve模型处理失败: failed to parse solve response JSON: invalid character '`' looking for beginning of value
2025/06/17 20:02:17 📝 [HANDLER] 记录解题日志

2025/06/17 20:02:17 [32m/Users/<USER>/Documents/Project/Solve/solve-go-api/internal/services/log_service.go:41
[0m[33m[129.352ms] [34;1m[rows:1][0m INSERT INTO `hook_solve_logs` (`app_id`,`user_url`,`matched_id`,`ocr_token`,`source`,`data`,`status`,`latency`,`created_at`) VALUES ('NdOCNqqWPtrLFvZj','http://img.igmdns.com/images/cc0001.jpg',0,1030,'',(NULL),0,18520,'2025-06-17 20:02:17.511')
2025/06/17 20:02:17 ❌ [HANDLER] 解题处理失败 - Status: 0, Message: failed to parse solve response JSON: invalid character '`' looking for beginning of value
time="2025-06-17T20:02:17+08:00" level=info msg="HTTP Request" body_size=114 client_ip="::1" error= latency=18.783076834s method=POST path=/api/v1/solve/question status_code=200 timestamp="2025-06-17T20:02:17+08:00" user_agent=PostmanRuntime-ApipostRuntime/1.1.0
[GIN] 2025/06/17 - 20:02:17 | 200 | 18.783176833s |             ::1 | POST     "/api/v1/solve/question"
